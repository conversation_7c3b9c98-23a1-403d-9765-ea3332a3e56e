# Seiri-chan 执行计划

本计划旨在分阶段构建 `Seiri-chan` 应用。我们将从项目的基础架构开始，逐步实现核心的后端逻辑和前端界面，确保在每个阶段结束时都有一个可验证的成果。

## 测试策略
- **测试框架**: 使用 Jest 作为主要测试框架
- **测试位置**: 所有测试文件放在 `__tests__` 目录下，按模块组织
- **测试命名**: 测试文件使用 `.test.ts` 后缀
- **测试覆盖**: 每个核心模块都应有对应的单元测试
- **测试运行**: 使用 `pnpm test` 命令运行所有测试
- **测试要求**: 每个单元完成后都应编写相应的测试用例

## 依赖关系图 (Mermaid)

为了更直观地展示各单元间的依赖关系，我们使用以下流程图：

```mermaid
graph TD
    subgraph "阶段一: 项目初始化与基础架构"
        U1_1["1.1 项目与环境设置"] --> U1_2;
        U1_2["1.2 数据库 Schema 定义 (Prisma)"] --> U1_3;
        U1_3["1.3 配置管理模块 (TOML)"] --> U1_4;
        U1_4["1.4 日志与错误处理模块"] --> U2_1;
    end

    subgraph "阶段二: 核心后端逻辑 (无AI)"
        U2_1["2.1 TMDB 客户端与缓存"] --> U2_3;
        U2_2["2.2 文件扫描、分类与本地元数据分析引擎"] --> U2_3;
        U2_3["2.3 传统识别引擎"] --> U2_4;
        U2_4["2.4 任务处理器 (Task Processor)"] --> U2_5;
        U2_5["2.5 文件操作模块"] --> U2_6;
        U2_6["2.6 WebSocket 服务基础"] --> U3_1;
    end

    subgraph "阶段三: 核心API接口"
        U3_1["3.1 任务管理API (/api/tasks)"] --> U3_2;
        U3_2["3.2 配置管理API (/api/config)"] --> U3_3;
        U3_3["3.3 文件浏览API (/api/utils/browse)"] --> U4_1;
    end

    subgraph "阶段四: 核心前端界面 (MVP)"
        U4_1["4.1 基础UI框架与布局"] --> U4_2 & U4_3 & U4_4;
        U4_2["4.2 设置页面UI"] --> U4_5;
        U4_3["4.3 添加任务向导UI"] --> U4_5;
        U4_4["4.4 任务看板UI (列表与卡片)"] --> U4_5;
        U4_5["4.5 前后端数据联调"] --> U4_6;
        U4_6["4.6 任务详情页与文件列表"] --> U5_1;
    end
    
    subgraph "阶段五: 核心流程闭环与优化"
        U5_1["5.1 任务重试与删除功能"] --> U5_2;
        U5_2["5.2 '移动'操作的健壮性实现"] --> U5_3;
        U5_3["5.3 国际化(i18n)支持"] --> U6_1;
    end

    subgraph "阶段六: AI识别与高级功能"
        U6_1["6.1 AI识别引擎模块"] --> U6_2;
        U6_2["6.2 AI结果解析与验证"] --> U6_3;
        U6_3["6.3 电影分离功能"] --> U6_4;
        U6_4["6.4 AI配置与测试UI"] --> U7_1;
    end

    subgraph "阶段七: 未来功能"
        U7_1["7.1 字幕整理"] --> U7_2;
        U7_2["7.2 预告片整理"] --> U7_3;
        U7_3["7.3 字体文件收集"];
    end

    U1_2 --> U2_4;
    U1_3 --> U2_4;
    U2_4 --> U3_1;
    U2_5 --> U3_1;
    U2_6 --> U3_1;
    U3_1 --> U4_5;
    U3_2 --> U4_5;
    U3_3 --> U4_5;
```

---

## 详细单元分解

### **阶段一: 项目初始化与基础架构 (Foundation)**
*目标: 搭建项目的骨架，为后续开发提供稳定的基础。*

- [x] **单元 1.1: 项目与环境设置**
  - **描述:** 初始化Next.js项目，使用pnpm作为包管理器。安装所有在`DESIGN.md`中列出的核心依赖 (Next.js, Prisma, Tailwind CSS, Zod, etc.)。配置TypeScript和ESLint。
  - **依赖:** 无
  - **文档:** [单元 1.1 完成文档](seiri-chan/docs/unit-1.1-project-setup.md)

- [x] **单元 1.2: 数据库 Schema 定义 (Prisma)**
  - **描述:** 根据`DESIGN.md`中的`schema.prisma`定义，创建所有模型（`MediaTask`, `FileTask`, `Tmdb...Cache`等）和枚举。生成初始的Prisma Client。
  - **依赖:** 1.1
  - **文档:** [单元 1.2 完成文档](seiri-chan/docs/unit-1.2-database-schema.md)

- [x] **单元 1.3: 配置管理模块 (`config.ts`)**
  - **描述:** 实现一个单例`ConfigManager`，负责在服务器启动时读取和解析`seiri.toml`配置文件。提供类型安全的`getConfig()`方法。
  - **依赖:** 1.1
  - **文档:** [单元 1.3 完成文档](seiri-chan/docs/unit-1.3-config-management.md)

- [x] **单元 1.4: 日志与错误处理模块**
  - **描述:** 建立基础的日志系统（例如使用 `pino`），并定义统一的API错误处理中间件或函数。
  - **依赖:** 1.1
  - **文档:** [单元 1.4 完成文档](seiri-chan/docs/unit-1.4-logging-error-handling.md)

---

### **阶段二: 核心后端逻辑 (Core Backend - Traditional Engine)**
*目标: 实现不依赖AI的、基于规则的完整任务处理流程。*

- [x] **单元 2.1: TMDB 客户端与缓存**
  - **描述:** 封装`tmdb-ts`客户端。实现TMDB API的请求逻辑，并集成Prisma缓存（`TmdbSearchCache`, `TmdbMediaCache`, `TmdbSeasonCache`），在请求前先检查缓存。
  - **依赖:** 1.2, 1.4
  - **文档:** [单元 2.1 完成文档](seiri-chan/docs/unit-2.1-tmdb-client-cache.md)

- [-] **单元 2.2: 文件扫描、分类与本地元数据分析引擎**
  - **描述:** 负责扫描用户指定的路径，遍历所有文件。根据后缀名和固定标签（如`NCOP`, `SP`）对文件进行分类，赋予`FileType`。使用`@remotion/media-parser`读取视频文件的本地元数据（时长、分辨率）。为每个媒体文件簇创建`FileTask`，并关联字幕等文件。
  - **依赖:** 1.4

- [ ] **单元 2.3: 传统识别引擎**
  - **描述:** 接收`FileTask`列表。对文件名进行深度清理（移除各类标签），获得干净的标题用于搜索。调用TMDB客户端查询媒体信息，并更新顶层`MediaTask`的`tmdbId`和`displayName`等。
  - **依赖:** 2.1, 2.2

- [ ] **单元 2.4: 任务处理器 (Task Processor)**
  - **描述:** 创建一个单例的后台任务处理器。实现内存任务队列，并能根据`DESIGN.md`中的流程处理任务：`SCANNING` -> `RECOGNITION` -> `MAPPING`。它将协调扫描引擎、识别引擎和文件操作模块，完成从文件分析到最终重命名的完整流程。
  - **依赖:** 1.2, 1.3, 2.3

- [ ] **单元 2.5: 文件操作模块**
  - **描述:** 实现对硬链接、软链接、复制、移动四种文件操作的封装。此阶段，"移动"可以先采用简单的`fs.rename`，健壮性优化在后续阶段进行。
  - **依赖:** 1.3

- [ ] **单元 2.6: WebSocket 服务基础 (`websocket.ts`)**
  - **描述:** 使用`socket.io`搭建基础的WebSocket服务，定义任务状态更新的事件（如`task:update`），并与任务处理器初步集成，使其能在状态变更时广播消息。
  - **依赖:** 2.4

---

### **阶段三: 核心API接口 (Core APIs)**
*目标: 创建前端与后端交互所需的全部API端点。*

- [ ] **单元 3.1: 任务管理API (`/api/tasks`)**
  - **描述:** 实现 `POST /api/tasks` (创建), `GET /api/tasks` (获取列表), 和 `POST /api/tasks/operation/{id}/retry` 的基本框架。创建任务时，将任务ID推入任务处理器的队列。
  - **依赖:** 2.3, 2.4, 2.5

- [ ] **单元 3.2: 配置管理API (`/api/config`)**
  - **描述:** 实现 `GET /api/config` 和 `PUT /api/config`。使用Zod进行服务端验证。
  - **依赖:** 1.3

- [ ] **单元 3.3: 文件浏览API (`/api/utils/browse`)**
  - **描述:** 实现一个安全的、仅暴露给后端的服务器文件系统浏览API，用于在“添加任务”时供用户选择路径。
  - **依赖:** 1.3

---

### **阶段四: 核心前端界面 (Core UI - MVP)**
*目标: 构建一个功能可用的最小化用户界面，实现核心操作闭环。*

- [ ] **单元 4.1: 基础UI框架与布局**
  - **描述:** 使用Tailwind CSS和Headless UI搭建应用的主体布局，包括导航栏、侧边栏和内容区域。
  - **依赖:** 3.3

- [ ] **单元 4.2: 设置页面UI**
  - **描述:** 使用React Hook Form和Zod创建设置表单，实现Tab切换（通用、AI、其他）。
  - **依赖:** 4.1

- [ ] **单元 4.3: 添加任务向导UI**
  - **描述:** 创建一个模态框或独立页面，引导用户选择任务类型、通过文件浏览器API选择路径、设置扫描深度等。
  - **依赖:** 4.1

- [ ] **单元 4.4: 任务看板UI (列表与卡片)**
  - **描述:** 创建任务看板页面，以卡片形式展示媒体任务，显示名称、状态和进度。
  - **依赖:** 4.1

- [ ] **单元 4.5: 前后端数据联调**
  - **描述:** 将设置页、添加任务向导、任务看板与后端API对接。使用SWR进行数据请求，并设置socket.io客户端监听任务更新，实现看板的实时刷新。
  - **依赖:** 3.1, 3.2, 3.3, 4.2, 4.3, 4.4

- [ ] **单元 4.6: 任务详情页与文件列表**
  - **描述:** 创建点击任务卡片后跳转的详情页，展示该任务下的所有`FileTask`列表及其状态（源路径、目标路径、状态）。
  - **依赖:** 4.5

---

### **阶段五: 核心流程闭环与优化 (Polish & Robustness)**
*目标: 完善核心体验，并增强系统的健壮性。*

- [ ] **单元 5.1: 任务重试与删除功能**
  - **描述:** 在前端任务看板和详情页上实现“重试”和“删除”按钮的功能，并对接后端API。
  - **依赖:** 4.6

- [ ] **单元 5.2: '移动'操作的健壮性实现**
  - **描述:** 按照`DESIGN.md`中的描述，重构文件操作模块，为“移动”模式实现“两阶段提交”（先预移动到临时目录，再最终移动）策略，并处理`FATAL_ERROR`状态。
  - **依赖:** 2.4

- [ ] **单元 5.3: 国际化 (i18n) 支持**
  - **描述:** 集成 `next-intl`，将UI中的所有硬编码文本替换为i18n key，并提供简体中文和英文的翻译文件。
  - **依赖:** 5.1

---

### **阶段六: AI识别与高级功能 (AI Integration)**
*目标: 集成AI引擎，作为传统引擎的补充和增强。*

- [ ] **单元 6.1: AI识别引擎模块 (`ai-engine.ts`)**
  - **描述:** 实现AI Prompt的构建逻辑。封装对至少一种AI Provider（如OpenAI, Gemini）的API调用。
  - **依赖:** 5.3

- [ ] **单元 6.2: AI结果解析与验证**
  - **描述:** 解析LLM返回的JSON数据，并使用Zod进行严格的结构验证。将验证后的结果转换为`FileTask`的更新。
  - **依赖:** 6.1

- [ ] **单元 6.3: 电影分离功能**
  - **描述:** 在AI结果解析中，处理`movies_to_process_separately`字段，并调用任务创建服务为识别出的电影创建新的`MediaTask`。
  - **依赖:** 6.2

- [ ] **单元 6.4: AI配置与测试UI**
  - **描述:** 在设置页面的“AI模式”Tab中，添加AI相关的配置项（API Key, Prompt等），并实现“连接测试”按钮功能。
  - **依赖:** 6.3

---

### **阶段七: 未来功能 (Future Features)**
*目标: 实现需求文档中列出的、优先级较低的未来功能。*

- [ ] **单元 7.1: 字幕整理**
  - **描述:** 扩展文件扫描和识别逻辑，以支持字幕文件，并实现`chs`/`cht`到标准后缀的转换。
  - **依赖:** 6.4

- [ ] **单元 7.2: 预告片整理**
  - **描述:** 识别预告片文件并将其整理到`trailers`子目录。
  - **依赖:** 7.1

- [ ] **单元 7.3: 字体文件收集**
  - **描述:** 识别`[font]`压缩包，并实现自动解压到指定文件夹的功能。
  - **依赖:** 7.2