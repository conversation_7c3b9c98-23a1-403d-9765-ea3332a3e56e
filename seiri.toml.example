# Seiri-chan 配置文件示例
# 复制此文件为 data/seiri.toml 并根据需要修改配置

[general]
# TMDB 配置
[general.tmdb]
accessToken = "your_tmdb_access_token_here"  # 可选：TMDB Access Token
language = "zh-CN"                 # 语言设置
region = "CN"                      # 地区设置

# 路径配置
[general.paths]
tempDir = "/path/to/temp"                   # 可选：临时目录（用于移动操作）
defaultSourceRoot = "/path/to/source/media" # 可选：默认原媒体文件库路径（UI中选择媒体文件路径时的父路径）

# 各媒体类型的输出路径配置
[general.paths.output]
anime = "/path/to/anime/library"            # 必填：动漫输出路径
tv = "/path/to/tv/library"                  # 必填：电视剧输出路径
movie = "/path/to/movie/library"            # 必填：电影输出路径
anime_movie = "/path/to/anime_movie/library" # 必填：动漫电影输出路径

# 默认设置
[general.defaults]
fileOperation = "copy"    # 默认文件操作：hardlink, softlink, copy, move, skip
mediaType = "anime"       # 默认媒体类型：anime, tv, movie, anime_movie
enableAI = false          # 是否启用 AI 模式

[ai]
provider = "openai"       # AI 提供商：openai, gemini, claude

# OpenAI 配置
[ai.openai]
apiKey = "your_openai_api_key_here"  # OpenAI API 密钥
baseURL = "https://api.openai.com/v1"  # API 基础 URL（可选）
model = "gpt-4o-mini"                # 模型名称
temperature = 0.1                    # 温度参数 (0-2)
maxTokens = 4000                     # 最大 token 数

# Gemini 配置
[ai.gemini]
apiKey = "your_gemini_api_key_here"  # Gemini API 密钥
model = "gemini-1.5-flash"           # 模型名称
temperature = 0.1                    # 温度参数 (0-2)
maxTokens = 4000                     # 最大 token 数

# Claude 配置
[ai.claude]
apiKey = "your_claude_api_key_here"  # Claude API 密钥
baseURL = "https://api.anthropic.com"  # API 基础 URL（可选）
model = "claude-3-haiku-20240307"    # 模型名称
temperature = 0.1                    # 温度参数 (0-1)
maxTokens = 4000                     # 最大 token 数

# AI 分析配置
[ai.analysis]
confidenceThreshold = "high"       # 置信度阈值（低于此值需要用户确认）: low, medium, high
enableMovieSeparation = true        # 是否启用电影分离功能
customPrompt = ""                   # 自定义 Prompt（可选）

[other]
# 日志配置
[other.logging]
level = "info"                      # 日志级别：debug, info, warn, error
enableFileLogging = true            # 是否启用文件日志
maxLogFiles = 10                    # 最大日志文件数量

# 性能配置
[other.performance]
maxConcurrentFiles = 5              # 并发文件处理数量
maxQueueLength = 100                # 任务队列最大长度

# 实验性功能
[other.experimental]
enableSubtitleOrganization = false  # 启用字幕整理
enableTrailerOrganization = false   # 启用预告片整理
enableFontCollection = false        # 启用字体文件收集
