/**
 * 文件扫描、分类与本地元数据分析引擎
 * 单元 2.2 的主入口模块 - 更新后的逻辑
 */

import { promises as fs } from 'fs';
import path from 'path';
import { FileType } from '@prisma/client';
import { logger } from '../logger';
import type { 
  ScanResult, 
  ScanOptions, 
  ScanProgress, 
  VideoFile, 
  SubtitleFile, 
  FontFile 
} from './types';
import { 
  classifyFiles, 
  shouldProcessFile,
  extractVideoFiles,
  extractSubtitleFiles,
  extractFontFiles
} from './classifier';
import { extractBatchVideoMetadata, getFileBasicInfo } from './metadata';
import { 
  findBatchAssociations, 
  getUnassociatedFiles,
  extractAllAssociatedFiles
} from './associations';

/**
 * 递归扫描目录获取所有文件
 * @param dirPath 目录路径
 * @param options 扫描选项
 * @param currentDepth 当前深度
 * @returns 文件路径列表
 */
async function scanDirectory(
  dirPath: string, 
  options: ScanOptions = {},
  currentDepth: number = 0
): Promise<string[]> {
  const { maxDepth = 10, includeHidden = false } = options;
  
  if (currentDepth > maxDepth) {
    logger.warn(`[文件扫描] 达到最大深度限制: ${dirPath}`);
    return [];
  }
  
  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });
    const files: string[] = [];
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      // 跳过隐藏文件/目录（除非明确启用）
      if (!includeHidden && entry.name.startsWith('.')) {
        continue;
      }
      
      if (entry.isDirectory()) {
        // 检查是否应该处理此目录
        if (shouldProcessFile(entry.name, '', true)) {
          const subFiles = await scanDirectory(fullPath, options, currentDepth + 1);
          files.push(...subFiles);
        }
      } else if (entry.isFile()) {
        files.push(fullPath);
      }
    }
    
    return files;
  } catch (error) {
    logger.error(`[文件扫描] 扫描目录失败: ${dirPath}`, { error });
    return [];
  }
}

/**
 * 处理扫描进度报告
 * @param currentFile 当前处理的文件
 * @param processedFiles 已处理文件数
 * @param totalFiles 总文件数
 * @param onProgress 进度回调函数
 */
function reportProgress(
  currentFile: string,
  processedFiles: number,
  totalFiles: number,
  onProgress?: (progress: ScanProgress) => void
) {
  if (onProgress) {
    const progress = totalFiles > 0 ? Math.round((processedFiles / totalFiles) * 100) : 0;
    onProgress({
      currentFile,
      processedFiles,
      totalFiles,
      progress,
    });
  }
}

/**
 * 主扫描函数 - 新逻辑
 * 扫描指定路径，进行文件分类和元数据提取
 * @param sourcePath 源路径
 * @param options 扫描选项
 * @returns 扫描结果
 */
export async function scanMediaFiles(
  sourcePath: string, 
  options: ScanOptions = {}
): Promise<ScanResult> {
  const startTime = Date.now();
  logger.info(`[文件扫描] 开始扫描: ${sourcePath}`);
  
  try {
    // 验证源路径
    const stats = await fs.stat(sourcePath);
    if (!stats.isDirectory()) {
      throw new Error(`源路径不是目录: ${sourcePath}`);
    }
    
    // 第一阶段：递归扫描所有文件
    logger.info(`[文件扫描] 阶段1: 递归扫描文件`);
    const allFiles = await scanDirectory(sourcePath, options);
    logger.info(`[文件扫描] 发现 ${allFiles.length} 个文件`);
    
    if (allFiles.length === 0) {
      logger.warn(`[文件扫描] 未发现任何文件: ${sourcePath}`);
      return {
        videoFiles: [],
        subtitleFiles: [],
        fontFiles: [],
        ignoredFiles: [],
      };
    }
    
    // 第二阶段：获取文件基本信息并分类
    logger.info(`[文件扫描] 阶段2: 文件分类`);
    const fileInfos: Array<{ name: string; extension: string; path: string; fileSize: bigint }> = [];
    
    for (let i = 0; i < allFiles.length; i++) {
      const filePath = allFiles[i];
      reportProgress(filePath, i, allFiles.length, options.onProgress);
      
      try {
        const basicInfo = await getFileBasicInfo(filePath);
        fileInfos.push({
          name: basicInfo.fileName,
          extension: basicInfo.extension,
          path: filePath,
          fileSize: basicInfo.fileSize,
        });
      } catch (error) {
        logger.warn(`[文件扫描] 无法获取文件信息: ${filePath}`, { error });
      }
    }
    
    // 进行文件分类
    const classification = classifyFiles(fileInfos);
    
    // 第三阶段：提取视频文件并获取元数据
    logger.info(`[文件扫描] 阶段3: 提取视频元数据`);
    const videoFiles = extractVideoFiles(classification.classifiedFiles);
    const videoPaths = videoFiles.map(f => f.path);
    const videoMetadataMap = await extractBatchVideoMetadata(videoPaths);
    
    // 第四阶段：关联文件匹配
    logger.info(`[文件扫描] 阶段4: 关联文件匹配`);
    // 所有候选文件 = 已分类文件 + 未分类文件（除了视频文件本身）
    const allCandidatePaths = [
      ...classification.classifiedFiles.filter(f => f.fileType === FileType.SUBTITLE).map(f => f.path),
      ...classification.unclassifiedFiles.map(f => f.path),
    ];
    
    const associations = findBatchAssociations(videoPaths, allCandidatePaths);
    
    // 第五阶段：构建最终结果
    logger.info(`[文件扫描] 阶段5: 构建扫描结果`);
    
    // 构建视频文件结果
    const resultVideoFiles: VideoFile[] = [];
    const associationMap = new Map(associations.map(a => [a.videoPath, a]));
    
    for (const videoFile of videoFiles) {
      const metadata = videoMetadataMap.get(videoFile.path);
      if (!metadata) {
        logger.warn(`[文件扫描] 未找到视频元数据: ${videoFile.path}`);
        continue;
      }
      
      const association = associationMap.get(videoFile.path);
      const associatedFiles = association?.associatedPaths || [];
      
      resultVideoFiles.push({
        path: videoFile.path,
        fileType: videoFile.fileType,
        metadata,
        associatedFiles,
      });
    }
    
    // 构建独立字幕文件结果（未被关联的字幕）
    const allAssociatedFiles = extractAllAssociatedFiles(associations);
    const classifiedSubtitleFiles = extractSubtitleFiles(classification.classifiedFiles);
    const unassociatedSubtitles = getUnassociatedFiles(
      classifiedSubtitleFiles.map(f => f.path), 
      allAssociatedFiles
    );
    
    const subtitleFiles: SubtitleFile[] = classifiedSubtitleFiles.filter(f => 
      unassociatedSubtitles.includes(f.path)
    );
    
    // 构建字体文件结果
    const fontFiles: FontFile[] = extractFontFiles(classification.classifiedFiles);
    
    const result: ScanResult = {
      videoFiles: resultVideoFiles,
      subtitleFiles,
      fontFiles,
      ignoredFiles: classification.ignoredFiles,
    };
    
    // 记录最终统计信息
    const duration = Date.now() - startTime;
    logger.info(`[文件扫描] 扫描完成 (${duration}ms):`, {
      视频文件: result.videoFiles.length,
      独立字幕: result.subtitleFiles.length,
      字体文件: result.fontFiles.length,
      忽略文件: result.ignoredFiles.length,
      总处理时间: `${duration}ms`,
    });
    
    // 最终进度报告
    reportProgress('扫描完成', allFiles.length, allFiles.length, options.onProgress);
    
    return result;
    
  } catch (error) {
    logger.error(`[文件扫描] 扫描失败: ${sourcePath}`, { error });
    throw error;
  }
}

/**
 * 快速扫描（仅文件分类，不提取元数据）
 * @param sourcePath 源路径
 * @param options 扫描选项
 * @returns 简化的扫描结果
 */
export async function quickScanMediaFiles(
  sourcePath: string,
  options: ScanOptions = {}
): Promise<{
  videoCount: number;
  subtitleCount: number;
  fontCount: number;
  ignoredCount: number;
  totalFiles: number;
}> {
  logger.info(`[快速扫描] 开始快速扫描: ${sourcePath}`);
  
  try {
    const stats = await fs.stat(sourcePath);
    if (!stats.isDirectory()) {
      throw new Error(`源路径不是目录: ${sourcePath}`);
    }
    
    const allFiles = await scanDirectory(sourcePath, options);
    const fileInfos: Array<{ name: string; extension: string; path: string; fileSize: bigint }> = [];
    
    for (const filePath of allFiles) {
      try {
        const basicInfo = await getFileBasicInfo(filePath);
        fileInfos.push({
          name: basicInfo.fileName,
          extension: basicInfo.extension,
          path: filePath,
          fileSize: basicInfo.fileSize,
        });
      } catch (error) {
        // 忽略无法访问的文件
      }
    }
    
    const classification = classifyFiles(fileInfos);
    const videoFiles = extractVideoFiles(classification.classifiedFiles);
    const subtitleFiles = extractSubtitleFiles(classification.classifiedFiles);
    const fontFiles = extractFontFiles(classification.classifiedFiles);
    
    const result = {
      videoCount: videoFiles.length,
      subtitleCount: subtitleFiles.length,
      fontCount: fontFiles.length,
      ignoredCount: classification.ignoredFiles.length,
      totalFiles: allFiles.length,
    };
    
    logger.info(`[快速扫描] 扫描完成:`, result);
    return result;
    
  } catch (error) {
    logger.error(`[快速扫描] 扫描失败: ${sourcePath}`, { error });
    throw error;
  }
}

// 导出所有相关类型和函数
export * from './types';
export * from './constants';
export * from './classifier';
export * from './metadata';
export * from './associations';