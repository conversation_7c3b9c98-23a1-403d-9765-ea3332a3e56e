#!/usr/bin/env node
/**
 * TMDB 验证脚本运行器
 * 使用 tsx 运行 TypeScript 验证脚本
 */

const { spawn } = require('child_process');
const path = require('path');

const scriptPath = path.join(__dirname, 'verify-tmdb-integration.ts');

console.log('🚀 启动 TMDB 集成验证...\n');

const child = spawn('npx', ['tsx', scriptPath], {
  stdio: 'inherit',
  cwd: path.dirname(__dirname), // seiri-chan 根目录
});

child.on('close', (code) => {
  if (code === 0) {
    console.log('\n✅ 验证完成');
  } else {
    console.log(`\n❌ 验证失败，退出代码: ${code}`);
    process.exit(code);
  }
});

child.on('error', (error) => {
  console.error('❌ 启动验证脚本失败:', error);
  process.exit(1);
});
