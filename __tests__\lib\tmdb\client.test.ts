// TMDB 客户端测试
import { PrismaClient } from '@prisma/client';
import { TmdbClient } from '../../../src/lib/tmdb/client';
import { TmdbClientConfig, TmdbMediaType } from '../../../src/lib/tmdb/types';

// 模拟 tmdb-ts
jest.mock('tmdb-ts', () => ({
  TMDB: jest.fn().mockImplementation(() => ({
    search: {
      multi: jest.fn(),
    },
    movies: {
      details: jest.fn(),
    },
    tvShows: {
      details: jest.fn(),
    },
    tvSeasons: {
      details: jest.fn(),
    },
    account: {
      details: jest.fn(),
    },
  })),
}));

// 模拟 Prisma 客户端
const mockPrisma = {
  tmdbSearchCache: {
    findUnique: jest.fn(),
    upsert: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  tmdbMediaCache: {
    findUnique: jest.fn(),
    upsert: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  tmdbSeasonCache: {
    findUnique: jest.fn(),
    upsert: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
} as unknown as PrismaClient;

describe('TmdbClient', () => {
  let tmdbClient: TmdbClient;
  let mockTmdbInstance: any;

  const config: TmdbClientConfig = {
    accessToken: 'test-access-token',
    language: 'zh-CN',
    region: 'CN',
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // 重新设置模拟
    const { TMDB } = require('tmdb-ts');
    mockTmdbInstance = {
      search: { multi: jest.fn() },
      movies: { details: jest.fn() },
      tvShows: { details: jest.fn() },
      tvSeasons: { details: jest.fn() },
      account: { details: jest.fn() },
    };

    // 模拟构造函数返回我们的模拟实例
    TMDB.mockImplementation(() => mockTmdbInstance);

    tmdbClient = new TmdbClient(config, mockPrisma);
  });

  describe('搜索功能', () => {
    // 模拟搜索结果数组（简化后的格式）
    const mockSearchResults = [
      {
        id: 603,
        media_type: 'movie' as const,
        title: 'The Matrix',
        overview: 'A computer hacker learns from mysterious rebels about the true nature of his reality.',
        poster_path: '/f89U3ADr1oiB1s9GkdPOEpXUk5H.jpg',
        release_date: '1999-03-30',
        vote_average: 8.2,
        vote_count: 24000,
      },
    ];

    // 模拟API返回的完整响应对象（用于API调用）
    const mockApiResponse = {
      page: 1,
      results: mockSearchResults,
      total_pages: 1,
      total_results: 1,
    };

    it('应该从缓存返回搜索结果', async () => {
      const query = 'The Matrix';
      const cacheKey = `multi:${query}:maxPages3:${config.language}:${config.region}`;

      // 模拟缓存命中 - 缓存现在直接存储结果数组
      (mockPrisma.tmdbSearchCache.findUnique as jest.Mock).mockResolvedValue({
        query: cacheKey,
        data: JSON.stringify(mockSearchResults),
        createdAt: new Date(),
      });

      const result = await tmdbClient.searchMulti(query);

      expect(result).toEqual(mockSearchResults);
      expect(mockTmdbInstance.search.multi).not.toHaveBeenCalled();
    });

    it('应该在缓存未命中时调用 API 并缓存结果', async () => {
      const query = 'The Matrix';

      // 模拟缓存未命中
      (mockPrisma.tmdbSearchCache.findUnique as jest.Mock).mockResolvedValue(null);
      // 模拟 API 调用 - 返回完整的响应对象
      mockTmdbInstance.search.multi.mockResolvedValue(mockApiResponse);
      // 模拟缓存保存
      (mockPrisma.tmdbSearchCache.upsert as jest.Mock).mockResolvedValue({});

      const result = await tmdbClient.searchMulti(query);

      // 期望返回结果数组（从API响应中提取的results）
      expect(result).toEqual(mockSearchResults);
      expect(mockTmdbInstance.search.multi).toHaveBeenCalledWith({
        query,
        page: 1,
        language: config.language,
      });
      expect(mockPrisma.tmdbSearchCache.upsert).toHaveBeenCalled();
    });

    it('应该处理搜索错误', async () => {
      const query = 'Invalid Query';
      const error = new Error('API Error');

      (mockPrisma.tmdbSearchCache.findUnique as jest.Mock).mockResolvedValue(null);
      mockTmdbInstance.search.multi.mockRejectedValue(error);

      await expect(tmdbClient.searchMulti(query)).rejects.toThrow('搜索媒体失败');
    });
  });

  describe('电影详情', () => {
    const mockMovieDetails = {
      id: 603,
      title: 'The Matrix',
      original_title: 'The Matrix',
      overview: 'A computer hacker learns from mysterious rebels about the true nature of his reality.',
      poster_path: '/f89U3ADr1oiB1s9GkdPOEpXUk5H.jpg',
      backdrop_path: '/fNG7i7RqMErkcqhohV2a6cV1Ehy.jpg',
      release_date: '1999-03-30',
      runtime: 136,
      vote_average: 8.2,
      vote_count: 24000,
      popularity: 85.965,
      adult: false,
      video: false,
      original_language: 'en',
      genres: [{ id: 28, name: 'Action' }],
      production_companies: [],
      production_countries: [],
      spoken_languages: [],
      status: 'Released',
      tagline: 'Welcome to the Real World.',
      budget: 63000000,
      revenue: 467222824,
      homepage: null,
      imdb_id: 'tt0133093',
    };

    it('应该从缓存返回电影详情', async () => {
      const movieId = 603;

      // 模拟缓存命中
      (mockPrisma.tmdbMediaCache.findUnique as jest.Mock).mockResolvedValue({
        tmdbId: movieId,
        mediaType: 'MOVIE',
        data: JSON.stringify(mockMovieDetails),
        createdAt: new Date(),
      });

      const result = await tmdbClient.getMovieDetails(movieId);

      expect(result).toEqual(mockMovieDetails);
      expect(mockTmdbInstance.movies.details).not.toHaveBeenCalled();
    });

    it('应该在缓存未命中时调用 API 并缓存结果', async () => {
      const movieId = 603;

      // 模拟缓存未命中
      (mockPrisma.tmdbMediaCache.findUnique as jest.Mock).mockResolvedValue(null);
      // 模拟 API 调用
      mockTmdbInstance.movies.details.mockResolvedValue(mockMovieDetails);
      // 模拟缓存保存
      (mockPrisma.tmdbMediaCache.upsert as jest.Mock).mockResolvedValue({});

      const result = await tmdbClient.getMovieDetails(movieId);

      expect(result).toEqual(mockMovieDetails);
      expect(mockTmdbInstance.movies.details).toHaveBeenCalledWith(movieId);
      expect(mockPrisma.tmdbMediaCache.upsert).toHaveBeenCalled();
    });
  });

  describe('电视剧详情', () => {
    const mockTvDetails = {
      id: 1396,
      name: 'Breaking Bad',
      original_name: 'Breaking Bad',
      overview: 'A high school chemistry teacher diagnosed with inoperable lung cancer...',
      poster_path: '/ggFHVNu6YYI5L9pCfOacjizRGt.jpg',
      backdrop_path: '/tsRy63Mu5cu8etL1X7ZLyf7UP1M.jpg',
      first_air_date: '2008-01-20',
      last_air_date: '2013-09-29',
      vote_average: 9.0,
      vote_count: 12000,
      popularity: 400.0,
      adult: false,
      original_language: 'en',
      origin_country: ['US'],
      genres: [{ id: 18, name: 'Drama' }],
      created_by: [],
      episode_run_time: [47],
      homepage: null,
      in_production: false,
      languages: ['en'],
      last_episode_to_air: null,
      next_episode_to_air: null,
      networks: [],
      number_of_episodes: 62,
      number_of_seasons: 5,
      production_companies: [],
      production_countries: [],
      seasons: [],
      spoken_languages: [],
      status: 'Ended',
      tagline: '',
      type: 'Scripted',
    };

    it('应该从缓存返回电视剧详情', async () => {
      const tvId = 1396;

      // 模拟缓存命中
      (mockPrisma.tmdbMediaCache.findUnique as jest.Mock).mockResolvedValue({
        tmdbId: tvId,
        mediaType: 'TV',
        data: JSON.stringify(mockTvDetails),
        createdAt: new Date(),
      });

      const result = await tmdbClient.getTvDetails(tvId);

      expect(result).toEqual(mockTvDetails);
      expect(mockTmdbInstance.tvShows.details).not.toHaveBeenCalled();
    });

    it('应该在缓存未命中时调用 API 并缓存结果', async () => {
      const tvId = 1396;

      // 模拟缓存未命中
      (mockPrisma.tmdbMediaCache.findUnique as jest.Mock).mockResolvedValue(null);
      // 模拟 API 调用
      mockTmdbInstance.tvShows.details.mockResolvedValue(mockTvDetails);
      // 模拟缓存保存
      (mockPrisma.tmdbMediaCache.upsert as jest.Mock).mockResolvedValue({});

      const result = await tmdbClient.getTvDetails(tvId);

      expect(result).toEqual(mockTvDetails);
      expect(mockTmdbInstance.tvShows.details).toHaveBeenCalledWith(tvId);
      expect(mockPrisma.tmdbMediaCache.upsert).toHaveBeenCalled();
    });
  });

  describe('连接测试', () => {
    it('应该在连接成功时返回成功结果', async () => {
      const mockResponse = {
        id: 12345,
        username: 'testuser',
        name: 'Test User',
      };

      mockTmdbInstance.account.details.mockResolvedValue(mockResponse);

      const result = await tmdbClient.testConnection();

      expect(result.success).toBe(true);
      expect(result.message).toBe('TMDB 连接正常');
      expect(mockTmdbInstance.account.details).toHaveBeenCalled();
    });

    it('应该在账户ID无效时返回失败结果', async () => {
      const mockResponse = {
        id: 0, // 无效的账户ID
        username: '',
        name: '',
      };

      mockTmdbInstance.account.details.mockResolvedValue(mockResponse);

      const result = await tmdbClient.testConnection();

      expect(result.success).toBe(false);
      expect(result.message).toBe('TMDB 连接异常: 未返回预期结果');
    });

    it('应该在连接失败时返回失败结果', async () => {
      const error = new Error('Network Error');

      mockTmdbInstance.account.details.mockRejectedValue(error);

      const result = await tmdbClient.testConnection();

      expect(result.success).toBe(false);
      expect(result.message).toBe('TMDB 连接失败: Network Error');
    });
  });

  describe('跳过缓存功能', () => {
    // 重用之前定义的模拟数据
    const skipCacheSearchResults = [
      {
        id: 603,
        media_type: 'movie' as const,
        title: 'The Matrix',
        overview: 'A computer hacker learns from mysterious rebels about the true nature of his reality.',
        poster_path: '/f89U3ADr1oiB1s9GkdPOEpXUk5H.jpg',
        release_date: '1999-03-30',
        vote_average: 8.2,
        vote_count: 24000,
      },
    ];

    const skipCacheApiResponse = {
      page: 1,
      results: skipCacheSearchResults,
      total_pages: 1,
      total_results: 1,
    };

    it('应该在 skipCache=true 时跳过搜索缓存', async () => {
      const query = 'The Matrix';

      // 模拟有缓存存在
      (mockPrisma.tmdbSearchCache.findUnique as jest.Mock).mockResolvedValue({
        query: `multi:${query}:maxPages1:${config.language}:${config.region}`,
        data: JSON.stringify(skipCacheSearchResults),
        createdAt: new Date(),
      });
      // 模拟 API 调用
      mockTmdbInstance.search.multi.mockResolvedValue(skipCacheApiResponse);
      // 模拟缓存保存
      (mockPrisma.tmdbSearchCache.upsert as jest.Mock).mockResolvedValue({});

      const result = await tmdbClient.searchMulti(query, 1, true);

      expect(result).toEqual(skipCacheSearchResults);
      // 验证没有尝试读取缓存
      expect(mockPrisma.tmdbSearchCache.findUnique).not.toHaveBeenCalled();
      // 验证调用了 API
      expect(mockTmdbInstance.search.multi).toHaveBeenCalled();
      // 验证仍然保存到缓存
      expect(mockPrisma.tmdbSearchCache.upsert).toHaveBeenCalled();
    });

    it('应该在 skipCache=true 时跳过电影详情缓存', async () => {
      const movieId = 603;
      const mockMovieDetails = {
        id: movieId,
        title: 'The Matrix',
        original_title: 'The Matrix',
        overview: 'A computer hacker learns...',
        poster_path: '/f89U3ADr1oiB1s9GkdPOEpXUk5H.jpg',
        backdrop_path: '/fNG7i7RqMErkcqhohV2a6cV1Ehy.jpg',
        release_date: '1999-03-30',
        runtime: 136,
        vote_average: 8.2,
        vote_count: 24000,
        popularity: 85.965,
        adult: false,
        video: false,
        original_language: 'en',
        genres: [{ id: 28, name: 'Action' }],
        production_companies: [],
        production_countries: [],
        spoken_languages: [],
        status: 'Released',
        tagline: 'Welcome to the Real World.',
        budget: 63000000,
        revenue: 467222824,
        homepage: null,
        imdb_id: 'tt0133093',
      };

      // 模拟有缓存存在
      (mockPrisma.tmdbMediaCache.findUnique as jest.Mock).mockResolvedValue({
        tmdbId: movieId,
        mediaType: 'MOVIE',
        data: JSON.stringify(mockMovieDetails),
        createdAt: new Date(),
      });
      // 模拟 API 调用
      mockTmdbInstance.movies.details.mockResolvedValue(mockMovieDetails);
      // 模拟缓存保存
      (mockPrisma.tmdbMediaCache.upsert as jest.Mock).mockResolvedValue({});

      const result = await tmdbClient.getMovieDetails(movieId, true);

      expect(result).toEqual(mockMovieDetails);
      // 验证没有尝试读取缓存
      expect(mockPrisma.tmdbMediaCache.findUnique).not.toHaveBeenCalled();
      // 验证调用了 API
      expect(mockTmdbInstance.movies.details).toHaveBeenCalled();
      // 验证仍然保存到缓存
      expect(mockPrisma.tmdbMediaCache.upsert).toHaveBeenCalled();
    });
  });

  describe('缓存清除功能', () => {
    it('应该清除所有缓存', async () => {
      const mockResult = { searchCount: 10, mediaCount: 5, seasonCount: 3 };

      // 模拟缓存服务的清除方法
      const clearAllCacheSpy = jest.spyOn(tmdbClient['cache'], 'clearAllCache').mockResolvedValue(mockResult);

      const result = await tmdbClient.clearAllCache();

      expect(result).toEqual(mockResult);
      expect(clearAllCacheSpy).toHaveBeenCalled();
    });

    it('应该清除搜索缓存', async () => {
      const mockCount = 8;

      const clearSearchCacheSpy = jest.spyOn(tmdbClient['cache'], 'clearSearchCache').mockResolvedValue(mockCount);

      const result = await tmdbClient.clearSearchCache();

      expect(result).toBe(mockCount);
      expect(clearSearchCacheSpy).toHaveBeenCalled();
    });

    it('应该清除媒体缓存', async () => {
      const mockCount = 6;

      const clearMediaCacheSpy = jest.spyOn(tmdbClient['cache'], 'clearMediaCache').mockResolvedValue(mockCount);

      const result = await tmdbClient.clearMediaCache();

      expect(result).toBe(mockCount);
      expect(clearMediaCacheSpy).toHaveBeenCalled();
    });

    it('应该清除季缓存', async () => {
      const mockCount = 4;

      const clearSeasonCacheSpy = jest.spyOn(tmdbClient['cache'], 'clearSeasonCache').mockResolvedValue(mockCount);

      const result = await tmdbClient.clearSeasonCache();

      expect(result).toBe(mockCount);
      expect(clearSeasonCacheSpy).toHaveBeenCalled();
    });
  });

  describe('配置更新', () => {
    it('应该更新客户端配置', () => {
      const newConfig = {
        language: 'en-US' as const,
        region: 'US',
      };

      tmdbClient.updateConfig(newConfig);

      // 验证配置已更新（通过后续的 API 调用参数验证）
      expect(() => tmdbClient.updateConfig(newConfig)).not.toThrow();
    });
  });
});
