/**
 * 关联文件匹配模块
 * 实现视频文件与字幕、字体等关联文件的匹配逻辑
 */

import path from 'path';
import { logger } from '../logger';
import { getBaseName } from './classifier';

/**
 * 文件信息接口
 */
interface FileInfo {
  path: string;
  name: string;
  baseName: string;
  extension: string;
}

/**
 * 关联匹配结果
 */
export interface AssociationResult {
  /** 视频文件路径 */
  videoPath: string;
  /** 关联的所有文件路径列表 */
  associatedPaths: string[];
}

/**
 * 创建文件信息对象
 * @param filePath 文件路径
 * @returns 文件信息
 */
function createFileInfo(filePath: string): FileInfo {
  const name = path.basename(filePath);
  const extension = path.extname(filePath);
  const baseName = getBaseName(name);
  
  return {
    path: filePath,
    name,
    baseName,
    extension,
  };
}

/**
 * 基于文件名匹配关联文件
 * 使用简单的文件名匹配规则：去除扩展名后完全相同
 * @param videoFile 视频文件信息
 * @param candidateFiles 候选文件列表
 * @returns 匹配的文件路径列表
 */
function matchByBaseName(videoFile: FileInfo, candidateFiles: FileInfo[]): string[] {
  const matches: string[] = [];
  
  for (const candidate of candidateFiles) {
    if (videoFile.baseName === candidate.baseName) {
      matches.push(candidate.path);
      logger.debug(`[关联匹配] 找到关联文件: ${videoFile.name} <-> ${candidate.name}`);
    }
  }
  
  return matches;
}

/**
 * 为单个视频文件查找关联文件 - 新逻辑
 * @param videoPath 视频文件路径
 * @param allCandidatePaths 所有候选文件路径列表（包括已分类和未分类文件）
 * @returns 关联的文件路径列表
 */
export function findAssociationsForVideo(
  videoPath: string,
  allCandidatePaths: string[]
): string[] {
  const videoFile = createFileInfo(videoPath);
  const candidateFiles = allCandidatePaths.map(createFileInfo);
  
  // 匹配所有关联文件
  const associatedFiles = matchByBaseName(videoFile, candidateFiles);
  
  logger.debug(`[关联匹配] 视频文件 ${videoFile.name} 关联了 ${associatedFiles.length} 个文件`);
  
  return associatedFiles;
}

/**
 * 批量处理视频文件的关联匹配 - 新逻辑
 * @param videoPaths 视频文件路径列表
 * @param allCandidatePaths 所有候选文件路径列表（包括已分类和未分类文件）
 * @returns 关联匹配结果列表
 */
export function findBatchAssociations(
  videoPaths: string[],
  allCandidatePaths: string[]
): AssociationResult[] {
  logger.info(`[关联匹配] 开始批量匹配: ${videoPaths.length} 个视频文件`);
  
  const results: AssociationResult[] = [];
  
  for (const videoPath of videoPaths) {
    const associatedPaths = findAssociationsForVideo(videoPath, allCandidatePaths);
    results.push({
      videoPath,
      associatedPaths,
    });
  }
  
  // 统计关联结果
  const totalAssociations = results.reduce((sum, result) => sum + result.associatedPaths.length, 0);
  
  logger.info(`[关联匹配] 批量匹配完成:`, {
    videos: videoPaths.length,
    totalAssociations,
  });
  
  return results;
}

/**
 * 获取未被关联的文件列表
 * @param allFiles 所有文件路径列表
 * @param associatedFiles 已关联的文件路径列表
 * @returns 未被关联的文件路径列表
 */
export function getUnassociatedFiles(allFiles: string[], associatedFiles: string[]): string[] {
  const associatedSet = new Set(associatedFiles);
  return allFiles.filter(file => !associatedSet.has(file));
}

/**
 * 从关联结果中提取所有已关联的文件
 * @param associations 关联结果列表
 * @returns 所有已关联的文件路径列表
 */
export function extractAllAssociatedFiles(associations: AssociationResult[]): string[] {
  const associatedFiles: string[] = [];
  
  for (const association of associations) {
    associatedFiles.push(...association.associatedPaths);
  }
  
  return associatedFiles;
}

/**
 * 高级匹配：支持模糊匹配和多种命名模式
 * 目前保留接口，后续可扩展实现更复杂的匹配逻辑
 * @param videoPath 视频文件路径
 * @param candidatePaths 候选文件路径列表
 * @returns 匹配的文件路径列表
 */
export function findAdvancedAssociations(
  videoPath: string,
  candidatePaths: string[]
): string[] {
  // 目前使用基础匹配逻辑
  // 后续可扩展支持：
  // 1. 季集信息匹配 (S01E01)
  // 2. 语言标识匹配 (.chs, .cht)
  // 3. 模糊匹配算法
  // 4. 自定义匹配规则
  
  const videoFile = createFileInfo(videoPath);
  const candidateFiles = candidatePaths.map(createFileInfo);
  
  return matchByBaseName(videoFile, candidateFiles);
}