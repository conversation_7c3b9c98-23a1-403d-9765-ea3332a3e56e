// 通用工具模块
export * from '../logger';
export * from '../errors';
export * from '../config';

// 重新导出常用的工具函数
import { logger, logUtils } from '../logger';
import { errorUtils, AppError, ErrorCode, ErrorSeverity } from '../errors';
import { getConfig, updateConfig } from '../config';

// 应用程序工具集合
export const appUtils = {
  // 日志相关
  log: logger,
  logUtils,
  
  // 错误处理相关
  errorUtils,
  AppError,
  ErrorCode,
  ErrorSeverity,
  
  // 配置相关
  getConfig,
  updateConfig,
  
  // 通用工具函数
  
  /**
   * 延迟执行
   */
  delay: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  
  /**
   * 重试函数
   */
  retry: async <T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    delayMs: number = 1000
  ): Promise<T> => {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        logger.warn(`重试第 ${attempt} 次失败`, { 
          error: lastError.message, 
          attempt, 
          maxAttempts 
        });
        
        if (attempt < maxAttempts) {
          await appUtils.delay(delayMs);
        }
      }
    }
    
    throw new AppError(
      `重试 ${maxAttempts} 次后仍然失败: ${lastError!.message}`,
      ErrorCode.UNKNOWN_ERROR,
      ErrorSeverity.MEDIUM,
      { originalError: lastError!, maxAttempts }
    );
  },
  
  /**
   * 安全的 JSON 解析
   */
  safeJsonParse: <T = any>(jsonString: string, defaultValue?: T): T | null => {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      logger.warn('JSON 解析失败', { jsonString, error: (error as Error).message });
      return defaultValue ?? null;
    }
  },
  
  /**
   * 安全的 JSON 字符串化
   */
  safeJsonStringify: (obj: any, space?: number): string => {
    try {
      return JSON.stringify(obj, null, space);
    } catch (error) {
      logger.warn('JSON 字符串化失败', { error: (error as Error).message });
      return '{}';
    }
  },
  
  /**
   * 格式化文件大小
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },
  
  /**
   * 格式化持续时间
   */
  formatDuration: (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
  },
  
  /**
   * 生成唯一 ID
   */
  generateId: (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },
  
  /**
   * 深度克隆对象
   */
  deepClone: <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T;
    }
    
    if (obj instanceof Array) {
      return obj.map(item => appUtils.deepClone(item)) as unknown as T;
    }
    
    if (typeof obj === 'object') {
      const cloned = {} as T;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = appUtils.deepClone(obj[key]);
        }
      }
      return cloned;
    }
    
    return obj;
  },
  
  /**
   * 防抖函数
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void => {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },
  
  /**
   * 节流函数
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void => {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },
  
  /**
   * 检查是否为有效的文件路径
   */
  isValidPath: (path: string): boolean => {
    if (!path || typeof path !== 'string') {
      return false;
    }
    
    // 基本的路径验证
    const invalidChars = /[<>:"|?*]/;
    return !invalidChars.test(path);
  },
  
  /**
   * 规范化路径分隔符
   */
  normalizePath: (path: string): string => {
    return path.replace(/\\/g, '/');
  },
  
  /**
   * 获取文件扩展名
   */
  getFileExtension: (filename: string): string => {
    const lastDot = filename.lastIndexOf('.');
    return lastDot === -1 ? '' : filename.slice(lastDot + 1).toLowerCase();
  },
  
  /**
   * 检查是否为视频文件
   */
  isVideoFile: (filename: string): boolean => {
    const videoExtensions = ['mp4', 'mkv', 'avi', 'mov', 'wmv', 'flv', 'webm', 'm4v', 'ts', 'mts'];
    const ext = appUtils.getFileExtension(filename);
    return videoExtensions.includes(ext);
  },
  
  /**
   * 检查是否为字幕文件
   */
  isSubtitleFile: (filename: string): boolean => {
    const subtitleExtensions = ['srt', 'ass', 'ssa', 'vtt', 'sub', 'idx', 'sup'];
    const ext = appUtils.getFileExtension(filename);
    return subtitleExtensions.includes(ext);
  },
};

// 导出默认工具集合
export default appUtils;
