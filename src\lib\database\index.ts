/**
 * 数据库客户端单例管理
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { logger } from '../logger';
import { DatabaseError } from '../errors';

// 全局 Prisma 客户端实例
let prismaInstance: PrismaClient<
        Prisma.PrismaClientOptions,
        Prisma.LogLevel
      > | null = null;

/**
 * 获取 Prisma 客户端实例（单例模式）
 */
export function getPrismaClient(): PrismaClient{
  if (!prismaInstance) {
    try {
      prismaInstance = new PrismaClient({
        log: [
          { level: 'error', emit: 'event' },
          { level: 'warn', emit: 'event' },
        ],
      });

      // 监听数据库错误事件
      prismaInstance.$on('error', (e) => {
        logger.error('数据库错误', { error: e });
      });

      prismaInstance.$on('warn', (e) => {
        logger.warn('数据库警告', { warning: e });
      });

      logger.info('Prisma 客户端单例已创建');
    } catch (error) {
      logger.error('创建 Prisma 客户端失败', { error });
      throw new DatabaseError('数据库连接失败', {
        originalError: error,
      });
    }
  }

  return prismaInstance;
}

/**
 * 关闭数据库连接
 */
export async function closePrismaClient(): Promise<void> {
  if (prismaInstance) {
    try {
      await prismaInstance.$disconnect();
      prismaInstance = null;
      logger.info('Prisma 客户端连接已关闭');
    } catch (error) {
      logger.error('关闭 Prisma 客户端连接失败', { error });
      throw new DatabaseError('关闭数据库连接失败', {
        originalError: error,
      });
    }
  }
}

/**
 * 测试数据库连接
 */
export async function testDatabaseConnection(): Promise<{ success: boolean; message: string }> {
  try {
    const prisma = getPrismaClient();
    
    // 执行一个简单的查询来测试连接
    await prisma.$queryRaw`SELECT 1`;
    
    logger.info('数据库连接测试成功');
    return {
      success: true,
      message: '数据库连接正常',
    };
  } catch (error) {
    logger.error('数据库连接测试失败', { error });
    
    let message = '数据库连接失败';
    if (error instanceof Error) {
      message += `: ${error.message}`;
    }
    
    return {
      success: false,
      message,
    };
  }
}

/**
 * 重置 Prisma 客户端实例（用于测试或重新连接）
 */
export async function resetPrismaClient(): Promise<void> {
  await closePrismaClient();
  logger.info('Prisma 客户端单例已重置');
}

// 进程退出时自动关闭数据库连接
process.on('beforeExit', async () => {
  await closePrismaClient();
});

process.on('SIGINT', async () => {
  await closePrismaClient();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closePrismaClient();
  process.exit(0);
});
