/**
 * 扫描模块数据库集成使用示例
 */

import { FileOperation } from '@prisma/client';
import { 
  scanMediaFilesAndSaveToDatabase,
  scanMediaFiles,
  saveScanResultToDatabase,
  getFileTaskStats
} from '../src/lib/scanner';

// 示例1: 完整的扫描并保存流程
async function example1_fullScanAndSave() {
  console.log('=== 示例1: 完整扫描并保存 ===');
  
  try {
    const stats = await scanMediaFilesAndSaveToDatabase(
      'example-media-task-1',
      '/path/to/anime/folder',
      FileOperation.COPY,
      {
        onProgress: (progress) => {
          console.log(`[${progress.stage}] ${progress.currentFile} - ${progress.progress}%`);
          
          if (progress.stage === 'saving' && progress.saveProgress) {
            console.log(`  保存进度: ${progress.saveProgress.savedTasks}/${progress.saveProgress.totalTasks}`);
          }
        }
      }
    );
    
    console.log('扫描完成:', stats);
  } catch (error) {
    console.error('扫描失败:', error);
  }
}

// 示例2: 分步操作（先扫描后保存）
async function example2_stepByStep() {
  console.log('=== 示例2: 分步操作 ===');
  
  try {
    // 步骤1: 仅扫描文件
    console.log('步骤1: 扫描文件...');
    const scanResult = await scanMediaFiles('/path/to/anime/folder', {
      onProgress: (progress) => {
        console.log(`扫描进度: ${progress.currentFile} - ${progress.progress}%`);
      }
    });
    
    console.log('扫描结果:', {
      videoFiles: scanResult.videoFiles.length,
      subtitleFiles: scanResult.subtitleFiles.length,
      fontFiles: scanResult.fontFiles.length,
      ignoredFiles: scanResult.ignoredFiles.length,
    });
    
    // 步骤2: 保存到数据库
    console.log('步骤2: 保存到数据库...');
    const stats = await saveScanResultToDatabase(
      'example-media-task-2',
      '/path/to/anime/folder',
      scanResult,
      FileOperation.HARDLINK,
      (progress) => {
        console.log(`保存进度: 批次 ${progress.currentBatch}/${progress.totalBatches} - ${progress.progress}%`);
      }
    );
    
    console.log('保存完成:', stats);
  } catch (error) {
    console.error('操作失败:', error);
  }
}

// 示例3: 获取文件任务统计
async function example3_getStats() {
  console.log('=== 示例3: 获取统计信息 ===');
  
  try {
    const stats = await getFileTaskStats('example-media-task-1');
    
    console.log('文件任务统计:', {
      总数: stats.total,
      按状态分组: stats.byStatus,
      按类型分组: stats.byType,
    });
  } catch (error) {
    console.error('获取统计失败:', error);
  }
}

// 示例4: 错误处理
async function example4_errorHandling() {
  console.log('=== 示例4: 错误处理 ===');
  
  try {
    // 尝试扫描不存在的路径
    await scanMediaFilesAndSaveToDatabase(
      'example-media-task-error',
      '/non/existent/path',
      FileOperation.COPY
    );
  } catch (error) {
    if (error instanceof Error) {
      console.log('捕获到错误:', {
        name: error.name,
        message: error.message,
        // 如果是 AppError，还可以获取更多信息
        ...(error as any).code && { code: (error as any).code },
        ...(error as any).severity && { severity: (error as any).severity },
      });
    }
  }
}

// 示例5: 快速扫描（不保存到数据库）
async function example5_quickScan() {
  console.log('=== 示例5: 快速扫描 ===');
  
  try {
    const result = await scanMediaFiles('/path/to/anime/folder', {
      maxDepth: 2, // 限制扫描深度
      onProgress: (progress) => {
        if (progress.processedFiles % 10 === 0) { // 每10个文件报告一次
          console.log(`已处理 ${progress.processedFiles}/${progress.totalFiles} 个文件`);
        }
      }
    });
    
    console.log('快速扫描结果:', {
      视频文件: result.videoFiles.length,
      字幕文件: result.subtitleFiles.length,
      字体文件: result.fontFiles.length,
      忽略文件: result.ignoredFiles.length,
    });
    
    // 显示前几个视频文件的详细信息
    console.log('前3个视频文件:');
    result.videoFiles.slice(0, 3).forEach((video, index) => {
      console.log(`  ${index + 1}. ${video.path}`);
      console.log(`     类型: ${video.fileType}`);
      console.log(`     大小: ${Number(video.metadata.fileSize)} 字节`);
      console.log(`     时长: ${video.metadata.duration || '未知'} 秒`);
      console.log(`     分辨率: ${video.metadata.resolution || '未知'}`);
      console.log(`     关联文件: ${video.associatedFiles.length} 个`);
    });
  } catch (error) {
    console.error('快速扫描失败:', error);
  }
}

// 运行所有示例
async function runAllExamples() {
  console.log('开始运行扫描模块数据库集成示例...\n');
  
  await example1_fullScanAndSave();
  console.log('\n');
  
  await example2_stepByStep();
  console.log('\n');
  
  await example3_getStats();
  console.log('\n');
  
  await example4_errorHandling();
  console.log('\n');
  
  await example5_quickScan();
  console.log('\n');
  
  console.log('所有示例运行完成！');
}

// 如果直接运行此文件
if (require.main === module) {
  runAllExamples().catch(console.error);
}

export {
  example1_fullScanAndSave,
  example2_stepByStep,
  example3_getStats,
  example4_errorHandling,
  example5_quickScan,
  runAllExamples,
};
