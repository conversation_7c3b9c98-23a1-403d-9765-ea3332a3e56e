# 单元 2.2: 文件扫描、分类与本地元数据分析引擎 - 数据库集成

## 概述
本文档描述了单元 2.2 的数据库集成实现，该模块负责扫描用户指定的路径，对文件进行分类和元数据提取，并将结果保存到数据库中的 FileTask 记录。

## 完成的工作

### 1. 错误处理扩展
在 `src/lib/errors/index.ts` 中添加了扫描相关的错误类型：
- `SCAN_FAILED` - 通用扫描失败
- `SCAN_PATH_NOT_FOUND` - 扫描路径不存在
- `SCAN_PATH_ACCESS_DENIED` - 扫描路径访问被拒绝
- `SCAN_METADATA_EXTRACTION_FAILED` - 元数据提取失败
- `ScanError` 类 - 扫描错误的专用类

### 2. 数据库客户端单例
创建了 `src/lib/database/index.ts`，提供：
- `getPrismaClient()` - 获取 Prisma 客户端单例
- `closePrismaClient()` - 关闭数据库连接
- `testDatabaseConnection()` - 测试数据库连接
- `resetPrismaClient()` - 重置客户端实例
- 自动处理进程退出时的连接清理

### 3. 数据库集成扫描服务
创建了 `src/lib/scanner/database.ts`，提供：

#### 主要功能
- `scanAndCreateFileTasks()` - 扫描文件并创建 FileTask 记录
- `getFileTaskStats()` - 获取文件任务统计信息

#### 核心特性
- **分阶段处理**: 扫描 → 转换 → 保存
- **批量保存**: 使用批处理避免单次事务过大
- **进度报告**: 详细的进度回调支持
- **错误处理**: 完善的错误分类和处理
- **相对路径**: 正确处理文件的相对路径

## API 接口

### scanAndCreateFileTasks
```typescript
async function scanAndCreateFileTasks(
  mediaTaskId: string,
  sourcePath: string,
  fileOperation: FileOperation,
  options?: DatabaseScanOptions
): Promise<ScanResultStats>
```

**参数**:
- `mediaTaskId` - 媒体任务ID
- `sourcePath` - 源路径
- `fileOperation` - 文件操作类型
- `options` - 扫描选项（包含进度回调）

**返回值**:
```typescript
interface ScanResultStats {
  videoTasks: number;      // 视频文件任务数
  subtitleTasks: number;   // 字幕文件任务数
  fontTasks: number;       // 字体文件任务数
  totalTasks: number;      // 总任务数
  ignoredFiles: number;    // 忽略文件数
}
```

### 进度回调
```typescript
interface DatabaseScanProgress {
  stage: 'scanning' | 'saving' | 'completed';
  currentFile: string;
  processedFiles: number;
  totalFiles: number;
  progress: number;
  savedTasks?: number;
  totalTasks?: number;
}
```

## 使用示例

### 基本使用
```typescript
import { scanAndCreateFileTasks } from '@/lib/scanner';
import { FileOperation } from '@prisma/client';

const stats = await scanAndCreateFileTasks(
  'media-task-id',
  '/path/to/source',
  FileOperation.COPY
);

console.log(`创建了 ${stats.totalTasks} 个文件任务`);
```

### 带进度回调
```typescript
const stats = await scanAndCreateFileTasks(
  'media-task-id',
  '/path/to/source',
  FileOperation.COPY,
  {
    onProgress: (progress) => {
      console.log(`${progress.stage}: ${progress.progress}%`);
      if (progress.stage === 'saving') {
        console.log(`已保存 ${progress.savedTasks}/${progress.totalTasks} 个任务`);
      }
    }
  }
);
```

### 获取统计信息
```typescript
import { getFileTaskStats } from '@/lib/scanner';

const stats = await getFileTaskStats('media-task-id');
console.log('文件任务统计:', stats);
// 输出: { total: 10, byStatus: { PENDING: 8, COMPLETED: 2 }, byType: { VIDEO_MAIN: 5, SUBTITLE: 3, FONT: 2 } }
```

## 数据转换逻辑

### VideoFile → FileTask
- 路径转换为相对路径
- 保存元数据（文件大小、时长、分辨率）
- 关联文件存储在 `associatedFiles` JSON 字段中

### SubtitleFile → FileTask
- 独立字幕文件（未被关联到视频文件）
- 文件类型设置为 `SUBTITLE`
- 无关联文件

### FontFile → FileTask
- 字体文件处理
- 文件类型设置为 `FONT`
- 无关联文件

## 错误处理

### 扫描错误
- `SCAN_PATH_NOT_FOUND` - 源路径不存在
- `SCAN_PATH_ACCESS_DENIED` - 权限不足
- `SCAN_FAILED` - 通用扫描失败

### 数据库错误
- `DATABASE_ERROR` - 数据库操作失败
- 自动重试机制（批处理失败时）

## 性能优化

### 批量处理
- 默认批处理大小：50 个记录
- 避免单次事务过大
- 支持 `skipDuplicates` 避免重复记录

### 内存管理
- 分阶段处理避免内存峰值
- 及时释放扫描结果

## 测试覆盖

### 单元测试
- 成功扫描和保存场景
- 各种错误情况处理
- 进度回调功能
- 统计信息获取

### 测试文件
- `__tests__/lib/scanner/database.test.ts`

## 集成说明

### 与现有扫描引擎的关系
- 复用现有的 `scanMediaFiles()` 函数
- 不修改现有扫描逻辑
- 仅添加数据库保存层

### 与任务管理器的集成
- 扫描模块不直接修改 MediaTask 状态
- 通过进度回调向上层报告状态
- 抛出详细的错误信息供任务管理器处理

## 下一步
- 单元 2.3: 传统识别引擎
- 任务管理器模块的实现
- WebSocket 进度推送集成
