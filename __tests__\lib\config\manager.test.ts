// 配置管理器测试
import { join } from 'path';
import { existsSync, writeFileSync, unlinkSync, mkdirSync } from 'fs';
import { ConfigManager } from '@/lib/config/manager';
import { DEFAULT_CONFIG, Config } from '@/lib/config/types';
import { getMediaOutputPath, getDefaultSourceRoot, getAllMediaOutputPaths } from '@/lib/config';

describe('ConfigManager', () => {
  let configManager: ConfigManager;
  let testConfigPath: string;
  let originalCwd: string;

  // 辅助函数：确保配置文件目录存在并写入文件
  const writeTestConfig = (content: string) => {
    const configDir = join(testConfigPath, '..');
    if (!existsSync(configDir)) {
      mkdirSync(configDir, { recursive: true });
    }
    writeFileSync(testConfigPath, content, 'utf-8');
  };

  beforeEach(() => {
    // 保存原始工作目录
    originalCwd = process.cwd();

    // 创建测试配置文件路径（现在在 data 子目录中）
    testConfigPath = join(process.cwd(), '__tests__', 'temp', 'data', 'seiri.toml');

    // 模拟 process.cwd() 返回测试目录
    jest.spyOn(process, 'cwd').mockReturnValue(join(process.cwd(), '__tests__', 'temp'));

    // 确保 data 目录存在
    const dataDir = join(process.cwd(), '__tests__', 'temp', 'data');
    if (!existsSync(dataDir)) {
      mkdirSync(dataDir, { recursive: true });
    }

    // 清理可能存在的测试配置文件
    if (existsSync(testConfigPath)) {
      unlinkSync(testConfigPath);
    }

    // 重置 ConfigManager 单例
    (ConfigManager as any).instance = undefined;
    configManager = ConfigManager.getInstance();
  });

  afterEach(() => {
    // 清理测试配置文件
    if (existsSync(testConfigPath)) {
      unlinkSync(testConfigPath);
    }
    
    // 恢复原始工作目录
    jest.restoreAllMocks();
  });

  describe('getInstance', () => {
    it('应该返回单例实例', () => {
      const instance1 = ConfigManager.getInstance();
      const instance2 = ConfigManager.getInstance();
      
      expect(instance1).toBe(instance2);
      expect(instance1).toBeInstanceOf(ConfigManager);
    });
  });

  describe('getConfig', () => {
    it('配置文件不存在时应该返回默认配置', () => {
      const config = configManager.getConfig();
      
      expect(config).toEqual(DEFAULT_CONFIG);
    });

    it('应该正确加载有效的配置文件', async () => {
      // 创建测试配置文件
      const testConfig = `
[general]
[general.tmdb]
accessToken = "test_api_key"
language = "en-US"

[general.paths]
defaultSourceRoot = "/test/source"

[general.paths.output]
anime = "/test/anime"
tv = "/test/tv"
movie = "/test/movie"
anime_movie = "/test/anime_movie"

[general.defaults]
enableAI = true
`;

      writeTestConfig(testConfig);
      
      // 重新加载配置
      const config = configManager.reloadConfig();
      
      expect(config.general.tmdb.accessToken).toBe('test_api_key');
      expect(config.general.tmdb.language).toBe('en-US');
      expect(config.general.paths.defaultSourceRoot).toBe('/test/source');
      expect(config.general.paths.output.anime).toBe('/test/anime');
      expect(config.general.paths.output.tv).toBe('/test/tv');
      expect(config.general.paths.output.movie).toBe('/test/movie');
      expect(config.general.paths.output.anime_movie).toBe('/test/anime_movie');
      expect(config.general.defaults.enableAI).toBe(true);
    });
  });

  describe('updateConfig', () => {
    it('应该正确更新配置', async () => {
      const updateData = {
        general: {
          tmdb: {
            accessToken: 'new_access_token',
            language: 'zh-CN' as const,
            region: 'CN',
          },
          paths: {
            defaultSourceRoot: '/test/source',
            output: {
              anime: '/test/anime',
              tv: '/test/tv',
              movie: '/test/movie',
              anime_movie: '/test/anime_movie',
            },
          },
          defaults: {
            fileOperation: 'copy' as const,
            mediaType: 'anime' as const,
            enableAI: true,
          },
        },
      };

      await configManager.updateConfig(updateData);

      const config = configManager.getConfig();
      expect(config.general.tmdb.accessToken).toBe('new_access_token');
      expect(config.general.paths.defaultSourceRoot).toBe('/test/source');
      expect(config.general.paths.output.anime).toBe('/test/anime');
      expect(config.general.paths.output.tv).toBe('/test/tv');
      expect(config.general.paths.output.movie).toBe('/test/movie');
      expect(config.general.paths.output.anime_movie).toBe('/test/anime_movie');
      expect(config.general.defaults.enableAI).toBe(true);
      // 其他配置应该保持默认值
      expect(config.general.tmdb.language).toBe('zh-CN');
    });

    it('应该验证配置格式', async () => {
      const invalidConfig = {
        general: {
          tmdb: {
            accessToken: 123, // 应该是字符串
          },
        },
      };

      await expect(configManager.updateConfig(invalidConfig as any))
        .rejects
        .toThrow();
    });
  });

  describe('configExists', () => {
    it('配置文件不存在时应该返回 false', () => {
      expect(configManager.configExists()).toBe(false);
    });

    it('配置文件存在时应该返回 true', async () => {
      await configManager.createDefaultConfig();
      expect(configManager.configExists()).toBe(true);
    });
  });

  describe('createDefaultConfig', () => {
    it('应该创建默认配置文件', async () => {
      await configManager.createDefaultConfig();
      
      expect(existsSync(testConfigPath)).toBe(true);
      
      const config = configManager.getConfig();
      expect(config).toEqual(DEFAULT_CONFIG);
    });
  });

  describe('validateConfig', () => {
    it('应该验证有效配置', () => {
      const validConfig = {
        ...DEFAULT_CONFIG,
        general: {
          ...DEFAULT_CONFIG.general,
          tmdb: {
            ...DEFAULT_CONFIG.general.tmdb,
            accessToken: 'test_access_token',
          },
          paths: {
            ...DEFAULT_CONFIG.general.paths,
            defaultSourceRoot: '/test/source',
            output: {
              anime: '/test/anime',
              tv: '/test/tv',
              movie: '/test/movie',
              anime_movie: '/test/anime_movie',
            },
          },
        },
      };

      expect(() => configManager.validateConfig(validConfig)).not.toThrow();
    });

    it('应该拒绝无效配置', () => {
      const invalidConfig = {
        general: {
          tmdb: {
            accessToken: 123, // 应该是字符串
          },
        },
      };

      expect(() => configManager.validateConfig(invalidConfig))
        .toThrow();
    });
  });

  describe('getGeneralConfig', () => {
    it('应该返回通用配置', () => {
      const generalConfig = configManager.getGeneralConfig();
      
      expect(generalConfig).toEqual(DEFAULT_CONFIG.general);
    });
  });

  describe('getAIConfig', () => {
    it('应该返回 AI 配置', () => {
      const aiConfig = configManager.getAIConfig();
      
      expect(aiConfig).toEqual(DEFAULT_CONFIG.ai);
    });
  });

  describe('getOtherConfig', () => {
    it('应该返回其他配置', () => {
      const otherConfig = configManager.getOtherConfig();

      expect(otherConfig).toEqual(DEFAULT_CONFIG.other);
    });
  });

  describe('新路径配置功能', () => {
    it('应该正确处理各媒体类型的输出路径配置', async () => {
      const pathConfig = {
        general: {
          paths: {
            defaultSourceRoot: '/source/media',
            output: {
              anime: '/output/anime',
              tv: '/output/tv',
              movie: '/output/movie',
              anime_movie: '/output/anime_movie',
            },
          },
        },
      };

      await configManager.updateConfig(pathConfig as any);
      const config = configManager.getConfig();

      expect(config.general.paths.defaultSourceRoot).toBe('/source/media');
      expect(config.general.paths.output.anime).toBe('/output/anime');
      expect(config.general.paths.output.tv).toBe('/output/tv');
      expect(config.general.paths.output.movie).toBe('/output/movie');
      expect(config.general.paths.output.anime_movie).toBe('/output/anime_movie');
    });

    it('应该验证媒体类型输出路径不能为空', async () => {
      const invalidConfig = {
        general: {
          paths: {
            output: {
              anime: '', // 不能为空
              tv: '/output/tv',
              movie: '/output/movie',
              anime_movie: '/output/anime_movie',
            },
          },
        },
      };

      await expect(configManager.updateConfig(invalidConfig as any))
        .rejects
        .toThrow();
    });

    it('应该正确加载包含新路径配置的TOML文件', () => {
      const testConfig = `
[general]
[general.tmdb]
accessToken = "test_token"

[general.paths]
defaultSourceRoot = "/source/root"

[general.paths.output]
anime = "/anime/output"
tv = "/tv/output"
movie = "/movie/output"
anime_movie = "/anime_movie/output"
`;

      writeTestConfig(testConfig);
      const config = configManager.reloadConfig();

      expect(config.general.paths.defaultSourceRoot).toBe('/source/root');
      expect(config.general.paths.output.anime).toBe('/anime/output');
      expect(config.general.paths.output.tv).toBe('/tv/output');
      expect(config.general.paths.output.movie).toBe('/movie/output');
      expect(config.general.paths.output.anime_movie).toBe('/anime_movie/output');
    });
  });

  describe('便捷函数测试', () => {
    beforeEach(async () => {
      // 重置全局单例以确保便捷函数使用正确的实例
      (ConfigManager as any).instance = configManager;

      // 设置测试配置
      const testConfig = {
        general: {
          paths: {
            defaultSourceRoot: '/test/source',
            output: {
              anime: '/test/anime',
              tv: '/test/tv',
              movie: '/test/movie',
              anime_movie: '/test/anime_movie',
            },
          },
        },
      };
      await configManager.updateConfig(testConfig as any);
    });

    it('getMediaOutputPath 应该返回指定媒体类型的输出路径', () => {
      expect(getMediaOutputPath('anime')).toBe('/test/anime');
      expect(getMediaOutputPath('tv')).toBe('/test/tv');
      expect(getMediaOutputPath('movie')).toBe('/test/movie');
      expect(getMediaOutputPath('anime_movie')).toBe('/test/anime_movie');
    });

    it('getDefaultSourceRoot 应该返回默认原媒体文件库路径', () => {
      expect(getDefaultSourceRoot()).toBe('/test/source');
    });

    it('getAllMediaOutputPaths 应该返回所有媒体类型的输出路径', () => {
      const allPaths = getAllMediaOutputPaths();
      expect(allPaths).toEqual({
        anime: '/test/anime',
        tv: '/test/tv',
        movie: '/test/movie',
        anime_movie: '/test/anime_movie',
      });
    });
  });
});
