// 错误处理模块
import { logger } from '../logger';

// 错误代码枚举
export enum ErrorCode {
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // 配置错误
  CONFIG_LOAD_ERROR = 'CONFIG_LOAD_ERROR',
  CONFIG_SAVE_ERROR = 'CONFIG_SAVE_ERROR',
  CONFIG_VALIDATION_ERROR = 'CONFIG_VALIDATION_ERROR',
  
  // 文件系统错误
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_ACCESS_DENIED = 'FILE_ACCESS_DENIED',
  DISK_SPACE_INSUFFICIENT = 'DISK_SPACE_INSUFFICIENT',
  FILE_OPERATION_FAILED = 'FILE_OPERATION_FAILED',
  
  // 任务错误
  TASK_NOT_FOUND = 'TASK_NOT_FOUND',
  TASK_ALREADY_RUNNING = 'TASK_ALREADY_RUNNING',
  TASK_EXECUTION_FAILED = 'TASK_EXECUTION_FAILED',
  
  // 网络错误
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  TMDB_API_ERROR = 'TMDB_API_ERROR',
  AI_API_ERROR = 'AI_API_ERROR',
  
  // 数据库错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  
  // 媒体分析错误
  MEDIA_ANALYSIS_FAILED = 'MEDIA_ANALYSIS_FAILED',
  MEDIA_NOT_FOUND = 'MEDIA_NOT_FOUND',
  INVALID_MEDIA_FORMAT = 'INVALID_MEDIA_FORMAT',

  // 扫描错误
  SCAN_FAILED = 'SCAN_FAILED',
  SCAN_PATH_NOT_FOUND = 'SCAN_PATH_NOT_FOUND',
  SCAN_PATH_ACCESS_DENIED = 'SCAN_PATH_ACCESS_DENIED',
  SCAN_METADATA_EXTRACTION_FAILED = 'SCAN_METADATA_EXTRACTION_FAILED',
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// 基础应用错误类
export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly context?: Record<string, any>;
  public readonly timestamp: Date;

  constructor(
    message: string,
    code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: Record<string, any>
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.severity = severity;
    this.context = context;
    this.timestamp = new Date();

    // 确保堆栈跟踪正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  // 转换为可序列化的对象
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      severity: this.severity,
      context: this.context,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack,
    };
  }
}

// 配置错误
export class ConfigError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, ErrorCode.CONFIG_LOAD_ERROR, ErrorSeverity.HIGH, context);
    this.name = 'ConfigError';
  }
}

// 文件操作错误
export class FileOperationError extends AppError {
  constructor(message: string, code: ErrorCode, context?: Record<string, any>) {
    super(message, code, ErrorSeverity.MEDIUM, context);
    this.name = 'FileOperationError';
  }
}

// 任务执行错误
export class TaskError extends AppError {
  constructor(message: string, code: ErrorCode, context?: Record<string, any>) {
    super(message, code, ErrorSeverity.MEDIUM, context);
    this.name = 'TaskError';
  }
}

// 网络/API 错误
export class NetworkError extends AppError {
  constructor(message: string, code: ErrorCode, context?: Record<string, any>) {
    super(message, code, ErrorSeverity.MEDIUM, context);
    this.name = 'NetworkError';
  }
}

// 数据库错误
export class DatabaseError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, ErrorCode.DATABASE_ERROR, ErrorSeverity.HIGH, context);
    this.name = 'DatabaseError';
  }
}

// 媒体分析错误
export class MediaAnalysisError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, ErrorCode.MEDIA_ANALYSIS_FAILED, ErrorSeverity.MEDIUM, context);
    this.name = 'MediaAnalysisError';
  }
}

// 扫描错误
export class ScanError extends AppError {
  constructor(message: string, code: ErrorCode = ErrorCode.SCAN_FAILED, context?: Record<string, any>) {
    super(message, code, ErrorSeverity.MEDIUM, context);
    this.name = 'ScanError';
  }
}

// 错误处理工具函数
export const errorUtils = {
  /**
   * 创建标准化的错误响应
   */
  createErrorResponse: (error: Error | AppError, requestId?: string) => {
    const isAppError = error instanceof AppError;
    
    return {
      success: false,
      error: {
        message: error.message,
        code: isAppError ? error.code : ErrorCode.UNKNOWN_ERROR,
        severity: isAppError ? error.severity : ErrorSeverity.MEDIUM,
        context: isAppError ? error.context : undefined,
        timestamp: isAppError ? error.timestamp : new Date(),
        requestId,
      },
    };
  },

  /**
   * 记录错误日志
   */
  logError: (error: Error | AppError, context?: Record<string, any>) => {
    const isAppError = error instanceof AppError;
    const logContext = {
      ...context,
      errorCode: isAppError ? error.code : ErrorCode.UNKNOWN_ERROR,
      errorSeverity: isAppError ? error.severity : ErrorSeverity.MEDIUM,
      errorContext: isAppError ? error.context : undefined,
    };

    if (isAppError && error.severity === ErrorSeverity.CRITICAL) {
      logger.fatal(logContext, error.message);
    } else {
      logger.error(logContext, error.message);
    }
  },

  /**
   * 包装异步函数，自动处理错误
   */
  wrapAsync: <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    errorCode?: ErrorCode,
    severity?: ErrorSeverity
  ) => {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args);
      } catch (error) {
        if (error instanceof AppError) {
          throw error;
        }
        
        const message = error instanceof Error ? error.message : '未知错误';
        throw new AppError(
          message,
          errorCode || ErrorCode.UNKNOWN_ERROR,
          severity || ErrorSeverity.MEDIUM,
          { originalError: error }
        );
      }
    };
  },

  /**
   * 从原生错误创建 AppError
   */
  fromNativeError: (
    error: Error,
    code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: Record<string, any>
  ): AppError => {
    return new AppError(error.message, code, severity, {
      ...context,
      originalStack: error.stack,
    });
  },

  /**
   * 检查错误是否为特定类型
   */
  isErrorCode: (error: Error | AppError, code: ErrorCode): boolean => {
    return error instanceof AppError && error.code === code;
  },

  /**
   * 检查错误严重级别
   */
  isErrorSeverity: (error: Error | AppError, severity: ErrorSeverity): boolean => {
    return error instanceof AppError && error.severity === severity;
  },
};

// API 错误处理中间件（用于 Next.js API 路由）
export const withErrorHandling = (
  handler: (req: any, res: any) => Promise<any>
) => {
  return async (req: any, res: any) => {
    try {
      return await handler(req, res);
    } catch (error) {
      errorUtils.logError(error as Error, {
        method: req.method,
        url: req.url,
        userAgent: req.headers['user-agent'],
      });

      const errorResponse = errorUtils.createErrorResponse(
        error as Error,
        req.headers['x-request-id']
      );

      const statusCode = error instanceof AppError && error.severity === ErrorSeverity.CRITICAL ? 500 : 400;
      
      res.status(statusCode).json(errorResponse);
    }
  };
};
