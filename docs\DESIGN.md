# Seiri-chan - 系统设计文档

## 1. 系统架构

### 1.1. 架构图

采用基于Next.js的前后端分离单体架构 (Monorepo)。

```mermaid
graph TD
    subgraph "用户浏览器"
        A[Next.js 前端]
    end

    subgraph "服务器 (Node.js 环境)"
        B[Next.js API Routes]
        C[任务管理器]
        D[扫描引擎]
        E{识别引擎}
        F[传统识别模块]
        G[AI 识别模块]
        H[文件操作模块]
        I[TOML 配置文件]
        J["SQLite 数据库 (Prisma)"]
    end

    subgraph "外部服务"
        K[TMDB API]
        L[AI Provider API]
    end

    A -- HTTP/WebSocket Request --> B
    B -- 创建/查询任务 --> C
    C -- 触发扫描 --> D
    D -- 提交识别 --> E
    E -- 选择模式 --> F & G
    F -- 获取元数据 --> K
    G -- 获取元数据 --> K
    G -- 发送分析请求 --> L
    E -- 生成文件映射 --> H
    H -- 执行文件操作 --> I/O(文件系统)
    B -- 读取/写入 --> I
    C -- 读/写任务状态 --> J
```

### 1.2. 技术栈
- **框架**: Next.js
- **包管理器**: pnpm
- **语言**: TypeScript
- **数据库 ORM**: Prisma (管理SQLite)
- **UI库**: Tailwind CSS + Headless UI
- **表单处理**: React Hook Form + Zod (用于验证)
- **状态管理**: Zustand
- **数据请求**: SWR
- **ws传输与自动降级**: socket.io
- **配置文件**: TOML (使用 `smol-toml` 解析)
- **TMDB客户端**: tmdb-ts (类型安全的API客户端)
- **视频元数据**: @remotion/media-parser (纯JS实现)
- **国际化 (i18n)**: next-intl

## 2. 数据库设计

使用 **SQLite** 数据库，并通过 **Prisma** 进行类型安全的操作。数据库将负责存储任务数据和TMDB缓存，为未来的测试用例导出和数据分析提供基础。任务的重要状态更新应当使用事件包裹。

### 2.1. Prisma Schema (`schema.prisma`)

```prisma
// schema.prisma
datasource db {
  provider = "sqlite"
  url      = "file:../data/seiri.db"
}

generator client {
  provider = "prisma-client-js"
}

// --- Enums for Type Safety ---

enum MediaTaskStatus {
  PENDING               // 任务已创建，等待处理
  SCANNING              // 正在扫描文件和分类
  RECOGNIZING           // 正在识别媒体信息 (TMDB/AI)
  ANALYSIS_FAILED       // 分析失败（如找不到匹配媒体）
  ANALYSIS_AWAITING_USER// 分析完成，但存在需要用户确认的情况
  PROCESSING            // 正在执行文件操作
  COMPLETED             // 所有文件任务成功
  CONFIRMATION_REQUIRED // 部分文件任务成功，个别需要用户确认
  FAILED                // 任务因可恢复错误失败
  FATAL_ERROR       // 移动模式下出现严重错误
}

enum FileTaskStatus {
  PENDING               // 等待分析
  IGNORED               // 未命中匹配规则或不在AI识别结果中，不处理
  MAPPED                // 已成功映射到目标，等待处理
  MISSING               // AI给出了不存在的源文件路径
  PROCESSING            // 文件复制或移动中
  COMPLETED             // 整理成功
  FILE_NOT_EXIST        // 源文件在处理时已不存在
  FAILED                // 文件整理失败
  FATAL_ERROR       // 移动模式出现严重错误
  CONFIRMATION_REQUIRED // AI置信度低或存在冲突，等待用户确认
}

enum FileType {
  VIDEO_MAIN    // 正片视频
  VIDEO_SPECIAL // 特典视频 (SP, OVA)
  VIDEO_TRAILER // 预告片 (Trailer, PV)
  VIDEO_EXTRA   // OP, ED 等
  VIDEO_UNKNOWN // 未知类型视频
  SUBTITLE      // 字幕文件
  FONT          // 字体文件
}

enum MediaType {
  ANIME
  TV
  MOVIE
  ANIME_MOVIE
}

enum FileOperation {
  HARDLINK
  SOFTLINK
  COPY
  MOVE
  SKIP  // for dry run
}

// --- Models ---

// 媒体整理的顶层任务
model MediaTask {
  id          String    @id @default(uuid())
  sourcePath  String
  // --- 状态与结果 ---
  status      MediaTaskStatus @default(PENDING)
  type        MediaType // "anime", "tv", "movie", "anime_movie"
  fileOperation FileOperation // 整理任务级的默认文件操作
  createdAt   DateTime  @default(now())
  finishedAt  DateTime?

  // --- 关联的元数据 ---
  tmdbId      Int?
  displayName String?
  displayYear Int?
  posterPath  String?

  fileTasks   FileTask[]
}

// 单个文件的任务记录
model FileTask {
  id            String    @id @default(uuid())
  sourcePath    String    // 原文件相对路径，需采用复合约束
  associatedFiles Json?   // 关联文件数组
  fileOperation FileOperation
  // --- 文件元数据 ---
  fileSize      BigInt?
  duration      Float?
  resolution    String?
  // --- 文件类型 ---
  fileType      FileType  @default(VIDEO_MAIN)

  // --- 分析与识别结果 ---
  status        FileTaskStatus @default(PENDING)
  
  // --- 目标信息 ---
  targetSeason  Int?      // 目标季号(仅剧集)
  targetEpisode Int?      // 目标集号(仅剧集)
  targetPath    String?   // 最终生成的目标相对路径
  
  // --- 错误与重试 ---
  // 用于存储结构化错误信息, e.g., { "code": "E_NO_SPACE", "message": "...", "details": "..." }
  errorMessage  Json?

  // --- 关联 ---
  mediaTask     MediaTask @relation(fields: [mediaTaskId], references: [id])
  mediaTaskId   String

  @@unique([mediaTaskId, sourcePath]) // 定义复合唯一键
}

// TMDB 搜索结果缓存
model TmdbSearchCache {
  query     String   @id // 搜索关键词作为主键
  data      String   // 完整的、拼接后的搜索结果JSON
  createdAt DateTime @default(now())
}

enum TmdbMediaType {
  MOVIE
  TV
}

// TMDB 媒体元数据缓存 (电影或电视剧)
model TmdbMediaCache {
  tmdbId    Int           @id // TMDB ID 作为主键
  mediaType TmdbMediaType // "movie" or "tv"
  data      String        // 完整的媒体元数据JSON
  createdAt DateTime      @default(now())
}

// TMDB 季数据缓存
model TmdbSeasonCache {
  // 复合主键：电视剧ID + 季度号
  tvId         Int
  seasonNumber Int
  data         String   // 完整的季数据JSON
  createdAt    DateTime @default(now())

  @@id([tvId, seasonNumber])
}
```

## 3. 核心流程设计

### 3.1. 任务创建流程
1.  **用户操作**: 用户在UI上选择路径，提交创建任务请求。
2.  **API接收**: `POST /api/tasks` 接口接收请求。
3.  **任务入库与入队**: 后端逻辑将请求展开为一个或多个 `MediaTask` 对象，写入数据库，初始状态为 `PENDING`。成功后，立即将新任务的ID **推送** 到一个常驻的内存任务队列中。
4.  **前端响应**: API立即返回成功响应，前端看板通过轮询或WebSocket获取到新任务并显示。(前端先使用 SWR 进行初始数据加载，之后用socket.io接受消息)

### 3.2. 任务执行流程
1.  **任务处理器**: 一个单例的、常驻后台的 **任务处理器 (Task Processor)** 持续监听任务队列。
2.  **任务出队**: 一旦队列中有任务ID，处理器立即将其取出，并从数据库中获取完整的 `MediaTask` 详情。
3.  **启动时加载**: 为确保健壮性，系统启动时，任务处理器会自动将数据库中所有处于 `PENDING` 状态的任务加载到队列中。同时提供一个手动刷新的API接口。
4.  **阶段一: 扫描、分类与本地元数据分析 (MediaTask Status: `SCANNING`)**
    - 取出任务，**通过事务**更新其状态为 `SCANNING`，并开始处理。
    - **扫描文件**: 扫描 `sourcePath`，获取所有文件和子目录。
    - **文件分类与筛选**: 遍历所有文件，根据**文件后缀名**和**固定的文件名标签**（如 `NCOP`, `SP`, `OVA`）对文件进行初步分类，赋予 `FileType`。未命中规则的文件(包括字幕文件)作为待匹配的关联文件。
    - **本地元数据分析**: 对于视频文件，使用 `@remotion/media-parser` 读取其**本地元数据**（时长、分辨率、编码等）。
    - **关联文件**: 匹配每个媒体文件在同路径下的的关联文件，暂时只用文件名完全相等的规则匹配。
    - **创建文件任务**: 为每个识别出的**主媒体文件**或**独立的特别篇**创建一个 `FileTask` 记录，包含路径、文件类型、本地元数据，并将关联文件以路径JSON数组的形式记录在 `associatedFiles` 字段；为剩下的、未被匹配为关联文件的字幕创建 `FileTask` 记录。

5.  **阶段二: 识别与TMDB元数据获取 (MediaTask Status: `RECOGNIZING`)**
    - `MediaTask` 状态更新为 `RECOGNIZING`。
    - **识别引擎**接收上一阶段生成的 `FileTask` 列表。
    - **文件名清理**: 对 `FileTask` 的文件名进行**深度清理**，移除标签，获得一个干净、可供搜索的媒体标题。
    - **TMDB查询**: 使用清理后的标题查询TMDB，找到最匹配的 `tmdbId`。
    - **更新媒体任务**: 成功识别后，更新顶层 `MediaTask` 的 `tmdbId`, `displayName`, `posterPath` 等信息。若无法识别，则 `MediaTask` 状态变为 `ANALYSIS_FAILED`，流程终止。
    - **获取TMDB元数据**: 从TMDB获取详细的媒体和季集信息，存入缓存。

6.  **阶段三: 深度分析与映射 (FileTask Status Updates)**
    - **分析每个文件**: 遍历所有 `fileType` 不为 `UNKNOWN` 的 `FileTask`。
    - **规则引擎**: 应用传统规则，尝试为 `FileTask` 匹配 `targetSeason` 和 `targetEpisode`，字体文件和字体压缩包整理至单独文件夹。
        - **成功**: `FileTask` 状态变为 `MAPPED`。
        - **无匹配**: `FileTask` 状态变为 `IGNORED`。
    - **AI引擎 (如启用)**: 将正片、特别篇、和未知媒体的 `FileTask` 提交给AI分析。
        - **高置信度成功**: `FileTask` 状态变为 `MAPPED`。
        - **低置信度**: `FileTask` 状态变为 `CONFIRMATION_REQUIRED`。
        - **无源文件路径对应的`FileTask`**: 添加一个状态为`MISSING`的特殊`FileTask`。这通常意味着本地存在对应的文件，但路径生成错误，可用于提醒用户。
        - **任务整体低置信度**: `MediaTask` 状态变为 `ANALYSIS_AWAITING_USER`，流程暂停，等待用户在UI上操作。
    - **检查任务状态**: 遍历完成后，检查所有 `FileTask` 的状态。
        - **若`MISSING`占比过高**: `MediaTask` 状态变为 `ANALYSIS_AWAITING_USER`，流程暂停，等待用户在UI上操作。
        - **否则**: 直接进入下一阶段。
7.  **阶段四: 执行文件操作 (MediaTask Status: `PROCESSING`)**
    - **事务性移动的“两阶段”策略**: 为了确保移动操作的原子性和健壮性，系统将采用一种更安全的“先复制到临时位置，再最终移动”的策略。此策略仅应用于“移动”模式。对于“复制”或“链接”模式，则直接执行操作。
    - **用户确认后或无需确认**: `MediaTask` 状态变为 `PROCESSING`。
    - **生成目标路径**: 为所有状态为 `MAPPED` 的 `FileTask` 生成最终的 `targetPath`。

    - **移动模式 - 阶段一: 准备与预移动 (Optimized Pre-move)**
        - **创建临时目录**: 在目标根目录下创建一个唯一的临时文件夹，如 `.tmp-seiri-{mediaTaskId}`。
        - **高效预移动到临时目录**: 遍历所有 `MAPPED` 的 `FileTask`，将每个源文件高效地预移动到其在临时目录中的最终位置 (`.tmp-seiri-{...}/<Show Name>/Season 01/S01E01.mkv`)。
            - **优先尝试硬链接**: 首先，尝试使用硬链接 (`fs.link`)。如果源路径和目标路径在同一个文件系统上，这是一个瞬时且不占用额外空间的操作。
            - **失败后回退到复制**: 如果创建硬链接失败（例如，因为源和目标在不同的文件系统/分区上，导致`EXDEV`错误），系统将自动回退到使用文件复制 (`fs.copyFile`)。
        - **阶段一失败处理**: 如果硬链接和复制两种方式都失败（例如磁盘空间不足、权限问题），则立即停止，删除整个临时目录，将失败的 `FileTask` 状态更新为 `FAILED`，并将 `MediaTask` 状态更新为 `FAILED`。这是一个简单的清理操作，因为源文件未受影响。

    - **移动模式 - 阶段二: 提交与清理 (Final Move & Cleanup)**
        - **原子性移动**: 如果阶段一所有文件都成功复制，则进入此阶段。这通常是同一文件系统下快速的元数据操作。将临时目录中的所有文件 **移动** 到其最终的目标位置。
        - **删除源文件**: 成功将所有文件移出临时目录后，开始删除原始的源文件。
        - **阶段二失败处理 (严重)**: 如果在原子性移动或删除源文件阶段失败（例如，跨盘符移动、权限变更），此时系统处于不一致状态。`MediaTask` 状态将被更新为 `FATAL_ERROR`。
        - **任务锁定**: `FATAL_ERROR` 状态会锁定任务，禁止任何自动或手动的重试操作。UI上应明确提示“任务发生严重错误，需要手动干预”，并提供导出任务数据和日志的选项，以便于开发者调试和手动恢复文件。

    - **复制/链接模式**:
        - 直接遍历所有 `MAPPED` 的 `FileTask`，执行对应的复制或链接操作。
        - 单个文件操作失败不会中断整个任务。失败的 `FileTask` 状态更新为 `FAILED`，并记录错误信息，然后继续处理下一个文件。

8.  **阶段五: 完成 (MediaTask Status: `COMPLETED` / `FAILED` / `CONFIRMATION_REQUIRED`)**
    - 所有 `MAPPED` 的文件任务处理完毕后，系统将根据所有 `FileTask` 的最终状态来决定 `MediaTask` 的最终状态。
    - **最终状态判断逻辑 (按优先级排序)**:
        1.  **检查严重失败**: 如果任何 `FileTask` 的状态为 `FATAL_ERROR`，则 `MediaTask` 的状态为 `FATAL_ERROR` (这是一个终态，优先级最高)。
        2.  **检查用户确认项**: 如果存在状态为 `MISSING` 或 `CONFIRMATION_REQUIRED` 的 `FileTask`，则 `MediaTask` 状态更新为 `CONFIRMATION_REQUIRED`。
        3.  **检查可恢复失败**: 如果存在状态为 `FAILED` 的 `FileTask`，则 `MediaTask` 状态更新为 `FAILED`。
        4.  **检查成功阈值**: 如果以上情况均未发生，则检查状态为 `COMPLETED` 的 `FileTask` 数量。
            - **成功**: 如果 `COMPLETED` 的数量大于等于一个预设的阈值（例如，电影至少1个，剧集至少2个），则 `MediaTask` 状态更新为 `COMPLETED`。
            - **失败**: 如果 `COMPLETED` 的数量低于阈值（包括0个），说明没有足够的核心文件被成功处理，则 `MediaTask` 状态更新为 `FAILED`。
    - **记录完成时间**: 为所有进入终态（非 `PROCESSING` 或 `SCANNING` 等）的任务更新 `finishedAt` 时间戳。

## 4. 模块设计

### 4.1. 配置文件模块 (`config.ts`)
- 使用 `smol-toml` 库解析 `seiri.toml` 文件。
- 提供一个单例 `ConfigManager`，在应用启动时加载配置，并提供类型安全的 `getConfig()` 方法。
- 在 `PUT /api/config` 中，提供安全的写入方法。

### 4.2. 表单处理
- 使用 `React Hook Form` 管理前端配置页面的表单状态。
- 使用 `Zod` 定义表单数据的schema，用于客户端和服务端的数据校验，确保数据一致性和安全性。

### 4.3. AI引擎 (`ai-engine.ts`)
- **Prompt构建**: 动态构建包含TMDB元数据、结构化本地文件列表、预分类标签和输出格式要求的Prompt。
- **客户端**: 封装与OpenAI、Gemini等大语言模型API的交互。
- **结果解析与验证**: 解析LLM返回的JSON，并使用Zod进行严格的结构验证。
- **功能实现**:
    - **电影分离**: 从AI结果中提取 `movies_to_process_separately` 字段，并调用任务创建服务。
    

### 4.4. WebSocket 服务 (`websocket.ts`)
- 使用 `socket.io` 库在Next.js的 API 路由中创建一个WebSocket服务器。
- 当任务状态或文件操作状态更新时，通过该服务向所有连接的前端客户端广播更新事件。

### 4.5. 任务管理器 (`task-manager.ts`)
- **任务创建**: 接收前端请求，展开为多个 `MediaTask`，并写入数据库。
- **任务重试**: 允许重试失败的整理任务和文件任务。对于失败的任务，将其ID重新推入任务队列即可。
- **文件任务修改**: 允许直接修改文件任务的`targetSeason` 和 `targetEpisode` 或`targetPath`，并触发重试。
- **任务导出**: 允许选择任务并导出。直接从数据库中读取 `MediaTask`, `FileTask` 和相关的TMDB缓存数据，组合成一个完整的、可复现的测试用例包。
- **任务队列**: 负责接收、存储和分发任务ID。
- **事务包裹**: MediaTask ⇄ FileTask 更新应使用Prisma 事务包裹。
- **任务处理器**: 单例运行，负责从队列中取出任务并执行完整的处理流程。