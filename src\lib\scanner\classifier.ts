/**
 * 文件分类模块
 * 根据文件名标签对视频文件进行分类
 */

import { FileType } from '@prisma/client';
import { logger } from '../logger';
import { 
  TAG_PATTERNS,
  getFileCategory,
  shouldIgnoreFile,
  shouldIgnoreDirectory
} from './constants';

/**
 * 对视频文件进行类型分类
 * @param fileName 文件名（包含扩展名）
 * @returns 文件类型
 */
export function classifyVideoFile(fileName: string): FileType {
  const fileNameLower = fileName.toLowerCase();
  
  logger.debug(`[文件分类] 开始分类文件: ${fileName}`);
  
  // 检查是否为预告片
  if (TAG_PATTERNS.TRAILER.test(fileNameLower)) {
    logger.debug(`[文件分类] 识别为预告片: ${fileName}`);
    return FileType.VIDEO_TRAILER;
  }
  
  // 检查是否为特别篇
  if (TAG_PATTERNS.SPECIAL.test(fileNameLower)) {
    logger.debug(`[文件分类] 识别为特别篇: ${fileName}`);
    return FileType.VIDEO_SPECIAL;
  }
  
  // 检查是否为特典视频 (OP/ED/NCOP/NCED等)
  if (TAG_PATTERNS.EXTRA.test(fileNameLower)) {
    logger.debug(`[文件分类] 识别为特典视频: ${fileName}`);
    return FileType.VIDEO_EXTRA;
  }
  
  // 默认分类为正片视频
  logger.debug(`[文件分类] 识别为正片视频: ${fileName}`);
  return FileType.VIDEO_MAIN;
}

/**
 * 根据文件扩展名获取基础文件类型
 * @param fileName 文件名
 * @param extension 文件扩展名
 * @returns 文件类型，如果不支持则返回 null
 */
export function getBaseFileType(fileName: string, extension: string): FileType | null {
  const category = getFileCategory(extension);
  
  switch (category) {
    case 'video':
      return classifyVideoFile(fileName);
    case 'subtitle':
      return FileType.SUBTITLE;
    case 'font':
      return FileType.FONT;
    default:
      return null;
  }
}

/**
 * 检查文件是否应该被处理
 * @param fileName 文件名
 * @param extension 文件扩展名
 * @param isDirectory 是否为目录
 * @returns 是否应该处理
 */
export function shouldProcessFile(fileName: string, extension: string, isDirectory: boolean = false): boolean {
  // 检查目录
  if (isDirectory) {
    if (shouldIgnoreDirectory(fileName)) {
      logger.debug(`[文件分类] 忽略目录: ${fileName}`);
      return false;
    }
    return true;
  }
  
  // 检查隐藏文件
  if (fileName.startsWith('.')) {
    logger.debug(`[文件分类] 忽略隐藏文件: ${fileName}`);
    return false;
  }
  
  // 不再使用 IGNORE_EXTENSIONS 忽略后缀，所有文件都进行处理
  return true;
}

/**
 * 批量分类文件 - 新逻辑
 * @param files 文件信息列表 {name: string, extension: string, path: string, fileSize: bigint}
 * @returns 分类结果
 */
export function classifyFiles(files: Array<{ name: string; extension: string; path: string; fileSize: bigint }>) {
  const result = {
    classifiedFiles: [] as Array<{ path: string; fileType: FileType; fileSize: bigint }>,
    unclassifiedFiles: [] as Array<{ path: string; fileSize: bigint }>,
    ignoredFiles: [] as string[],
  };
  
  for (const file of files) {
    if (!shouldProcessFile(file.name, file.extension)) {
      result.ignoredFiles.push(file.path);
      continue;
    }
    
    const fileType = getBaseFileType(file.name, file.extension);
    
    if (fileType) {
      // 规则命中的文件放入待处理列表
      result.classifiedFiles.push({
        path: file.path,
        fileType,
        fileSize: file.fileSize,
      });
    } else {
      // 其余文件放入待匹配列表
      result.unclassifiedFiles.push({
        path: file.path,
        fileSize: file.fileSize,
      });
    }
  }
  
  logger.info(`[文件分类] 分类完成 - 已分类: ${result.classifiedFiles.length}, 待匹配: ${result.unclassifiedFiles.length}, 忽略: ${result.ignoredFiles.length}`);
  
  return result;
}

/**
 * 从已分类文件中提取指定类型的文件
 * @param classifiedFiles 已分类文件列表
 * @param fileTypes 要提取的文件类型
 * @returns 指定类型的文件列表
 */
export function extractFilesByType(
  classifiedFiles: Array<{ path: string; fileType: FileType; fileSize: bigint }>,
  fileTypes: FileType[]
): Array<{ path: string; fileType: FileType; fileSize: bigint }> {
  return classifiedFiles.filter(file => fileTypes.includes(file.fileType));
}

/**
 * 从已分类文件中提取视频文件
 * @param classifiedFiles 已分类文件列表
 * @returns 视频文件列表
 */
export function extractVideoFiles(
  classifiedFiles: Array<{ path: string; fileType: FileType; fileSize: bigint }>
): Array<{ path: string; fileType: FileType; fileSize: bigint }> {
  const videoTypes = [
    FileType.VIDEO_MAIN,
    FileType.VIDEO_SPECIAL,
    FileType.VIDEO_TRAILER,
    FileType.VIDEO_EXTRA,
    FileType.VIDEO_UNKNOWN,
  ];
  return extractFilesByType(classifiedFiles, videoTypes);
}

/**
 * 从已分类文件中提取字幕文件
 * @param classifiedFiles 已分类文件列表
 * @returns 字幕文件列表
 */
export function extractSubtitleFiles(
  classifiedFiles: Array<{ path: string; fileType: FileType; fileSize: bigint }>
): Array<{ path: string; fileSize: bigint }> {
  return extractFilesByType(classifiedFiles, [FileType.SUBTITLE])
    .map(file => ({ path: file.path, fileSize: file.fileSize }));
}

/**
 * 从已分类文件中提取字体文件
 * @param classifiedFiles 已分类文件列表
 * @returns 字体文件列表
 */
export function extractFontFiles(
  classifiedFiles: Array<{ path: string; fileType: FileType; fileSize: bigint }>
): Array<{ path: string; fileSize: bigint }> {
  return extractFilesByType(classifiedFiles, [FileType.FONT])
    .map(file => ({ path: file.path, fileSize: file.fileSize }));
}

/**
 * 从文件名中提取基础名称（去除扩展名）
 * @param fileName 文件名
 * @returns 基础名称
 */
export function getBaseName(fileName: string): string {
  const lastDotIndex = fileName.lastIndexOf('.');
  return lastDotIndex === -1 ? fileName : fileName.substring(0, lastDotIndex);
}

/**
 * 检查两个文件名是否可能为关联文件
 * 基于文件名去除扩展名后是否相同
 * @param videoFileName 视频文件名
 * @param otherFileName 其他文件名
 * @returns 是否为关联文件
 */
export function isAssociatedFile(videoFileName: string, otherFileName: string): boolean {
  const videoBaseName = getBaseName(videoFileName);
  const otherBaseName = getBaseName(otherFileName);
  
  return videoBaseName === otherBaseName;
}