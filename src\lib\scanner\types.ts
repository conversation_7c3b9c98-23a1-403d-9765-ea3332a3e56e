/**
 * 文件扫描模块的类型定义
 */

import { FileType } from '@prisma/client';

/**
 * 视频文件元数据
 */
export interface VideoMetadata {
  /** 文件大小（字节） */
  fileSize: bigint;
  /** 视频时长（秒） */
  duration?: number;
  /** 视频分辨率（如 "1920x1080"） */
  resolution?: string;
}

/**
 * 扫描到的视频文件
 */
export interface VideoFile {
  /** 文件相对路径 */
  path: string;
  /** 文件类型分类 */
  fileType: FileType;
  /** 视频元数据 */
  metadata: VideoMetadata;
  /** 关联文件路径列表 */
  associatedFiles: string[];
}

/**
 * 扫描到的已分类文件
 */
export interface ClassifiedFile {
  /** 文件相对路径 */
  path: string;
  /** 文件类型分类 */
  fileType: FileType;
  /** 文件大小（字节） */
  fileSize: bigint;
}

/**
 * 扫描到的待匹配文件
 */
export interface UnclassifiedFile {
  /** 文件相对路径 */
  path: string;
  /** 文件大小（字节） */
  fileSize: bigint;
}

/**
 * 扫描到的字幕文件
 */
export interface SubtitleFile {
  /** 文件相对路径 */
  path: string;
  /** 文件大小（字节） */
  fileSize: bigint;
}

/**
 * 扫描到的字体文件
 */
export interface FontFile {
  /** 文件相对路径 */
  path: string;
  /** 文件大小（字节） */
  fileSize: bigint;
}

/**
 * 文件扫描结果
 */
export interface ScanResult {
  /** 视频文件列表 */
  videoFiles: VideoFile[];
  /** 独立的字幕文件列表（未被关联到视频文件） */
  subtitleFiles: SubtitleFile[];
  /** 字体文件列表 */
  fontFiles: FontFile[];
  /** 被忽略的文件路径列表 */
  ignoredFiles: string[];
}

/**
 * 扫描进度报告
 */
export interface ScanProgress {
  /** 当前扫描的文件路径 */
  currentFile: string;
  /** 已处理文件数量 */
  processedFiles: number;
  /** 总文件数量 */
  totalFiles: number;
  /** 进度百分比 (0-100) */
  progress: number;
}

/**
 * 扫描选项
 */
export interface ScanOptions {
  /** 是否包含隐藏文件 */
  includeHidden?: boolean;
  /** 最大扫描深度 */
  maxDepth?: number;
  /** 进度回调函数 */
  onProgress?: (progress: ScanProgress) => void;
}