// TMDB 缓存服务
import { PrismaClient } from '@prisma/client';
import { logger } from '../logger';
import {
  TmdbSearchResultItem,
  TmdbMovieDetails,
  TmdbTvDetails,
  TmdbSeasonDetails,
  TmdbMediaType,
  TmdbError,
} from './types';

export class TmdbCacheService {
  constructor(private prisma: PrismaClient) {}

  /**
   * 获取搜索结果缓存
   */
  async getSearchCache(query: string): Promise<TmdbSearchResultItem[] | null> {
    try {
      const cache = await this.prisma.tmdbSearchCache.findUnique({
        where: { query },
      });

      if (!cache) {
        logger.debug(`搜索缓存未命中: ${query}`);
        return null;
      }

      // 检查缓存是否过期（7天）
      const cacheAge = Date.now() - cache.createdAt.getTime();
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天

      if (cacheAge > maxAge) {
        logger.debug(`搜索缓存已过期: ${query}`);
        // 异步删除过期缓存
        this.prisma.tmdbSearchCache.delete({ where: { query } }).catch((error) => {
          logger.warn(`删除过期搜索缓存失败: ${error.message}`);
        });
        return null;
      }

      logger.debug(`搜索缓存命中: ${query}`);
      return JSON.parse(cache.data) as TmdbSearchResultItem[];
    } catch (error) {
      logger.error(`获取搜索缓存失败: ${error}`);
      return null;
    }
  }

  /**
   * 设置搜索结果缓存
   */
  async setSearchCache(query: string, data: TmdbSearchResultItem[]): Promise<void> {
    try {
      await this.prisma.tmdbSearchCache.upsert({
        where: { query },
        update: {
          data: JSON.stringify(data),
          createdAt: new Date(),
        },
        create: {
          query,
          data: JSON.stringify(data),
        },
      });

      logger.debug(`搜索缓存已保存: ${query}`);
    } catch (error) {
      logger.error(`保存搜索缓存失败: ${error}`);
      throw new TmdbError(
        '保存搜索缓存失败',
        'CACHE_SAVE_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 获取媒体详情缓存
   */
  async getMediaCache(
    tmdbId: number,
    mediaType: TmdbMediaType
  ): Promise<TmdbMovieDetails | TmdbTvDetails | null> {
    try {
      const cache = await this.prisma.tmdbMediaCache.findUnique({
        where: { tmdbId },
      });

      if (!cache) {
        logger.debug(`媒体缓存未命中: ${tmdbId} (${mediaType})`);
        return null;
      }

      // 检查媒体类型是否匹配
      if (cache.mediaType !== mediaType.toUpperCase()) {
        logger.debug(`媒体类型不匹配: ${tmdbId} (期望: ${mediaType}, 实际: ${cache.mediaType})`);
        return null;
      }

      // 检查缓存是否过期（30天）
      const cacheAge = Date.now() - cache.createdAt.getTime();
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天

      if (cacheAge > maxAge) {
        logger.debug(`媒体缓存已过期: ${tmdbId}`);
        // 异步删除过期缓存
        this.prisma.tmdbMediaCache.delete({ where: { tmdbId } }).catch((error) => {
          logger.warn(`删除过期媒体缓存失败: ${error.message}`);
        });
        return null;
      }

      logger.debug(`媒体缓存命中: ${tmdbId} (${mediaType})`);
      return JSON.parse(cache.data) as TmdbMovieDetails | TmdbTvDetails;
    } catch (error) {
      logger.error(`获取媒体缓存失败: ${error}`);
      return null;
    }
  }

  /**
   * 设置媒体详情缓存
   */
  async setMediaCache(
    tmdbId: number,
    mediaType: TmdbMediaType,
    data: TmdbMovieDetails | TmdbTvDetails
  ): Promise<void> {
    try {
      await this.prisma.tmdbMediaCache.upsert({
        where: { tmdbId },
        update: {
          mediaType: mediaType.toUpperCase() as 'MOVIE' | 'TV',
          data: JSON.stringify(data),
          createdAt: new Date(),
        },
        create: {
          tmdbId,
          mediaType: mediaType.toUpperCase() as 'MOVIE' | 'TV',
          data: JSON.stringify(data),
        },
      });

      logger.debug(`媒体缓存已保存: ${tmdbId} (${mediaType})`);
    } catch (error) {
      logger.error(`保存媒体缓存失败: ${error}`);
      throw new TmdbError(
        '保存媒体缓存失败',
        'CACHE_SAVE_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 获取季详情缓存
   */
  async getSeasonCache(tvId: number, seasonNumber: number): Promise<TmdbSeasonDetails | null> {
    try {
      const cache = await this.prisma.tmdbSeasonCache.findUnique({
        where: {
          tvId_seasonNumber: {
            tvId,
            seasonNumber,
          },
        },
      });

      if (!cache) {
        logger.debug(`季缓存未命中: TV${tvId} S${seasonNumber}`);
        return null;
      }

      // 检查缓存是否过期（30天）
      const cacheAge = Date.now() - cache.createdAt.getTime();
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天

      if (cacheAge > maxAge) {
        logger.debug(`季缓存已过期: TV${tvId} S${seasonNumber}`);
        // 异步删除过期缓存
        this.prisma.tmdbSeasonCache.delete({
          where: {
            tvId_seasonNumber: {
              tvId,
              seasonNumber,
            },
          },
        }).catch((error) => {
          logger.warn(`删除过期季缓存失败: ${error.message}`);
        });
        return null;
      }

      logger.debug(`季缓存命中: TV${tvId} S${seasonNumber}`);
      return JSON.parse(cache.data) as TmdbSeasonDetails;
    } catch (error) {
      logger.error(`获取季缓存失败: ${error}`);
      return null;
    }
  }

  /**
   * 设置季详情缓存
   */
  async setSeasonCache(
    tvId: number,
    seasonNumber: number,
    data: TmdbSeasonDetails
  ): Promise<void> {
    try {
      await this.prisma.tmdbSeasonCache.upsert({
        where: {
          tvId_seasonNumber: {
            tvId,
            seasonNumber,
          },
        },
        update: {
          data: JSON.stringify(data),
          createdAt: new Date(),
        },
        create: {
          tvId,
          seasonNumber,
          data: JSON.stringify(data),
        },
      });

      logger.debug(`季缓存已保存: TV${tvId} S${seasonNumber}`);
    } catch (error) {
      logger.error(`保存季缓存失败: ${error}`);
      throw new TmdbError(
        '保存季缓存失败',
        'CACHE_SAVE_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanupExpiredCache(): Promise<void> {
    try {
      const now = new Date();
      const searchCacheExpiry = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 7天前
      const mediaCacheExpiry = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30天前

      // 先统计过期的季缓存数量（在级联删除之前）
      const expiredSeasonCount = await this.prisma.tmdbSeasonCache.count({
        where: {
          createdAt: {
            lt: mediaCacheExpiry,
          },
        },
      });

      // 并行清理过期的搜索缓存和媒体缓存（媒体缓存删除会级联删除相关季缓存）
      const [deletedSearchCache, deletedMediaCache] = await Promise.all([
        this.prisma.tmdbSearchCache.deleteMany({
          where: {
            createdAt: {
              lt: searchCacheExpiry,
            },
          },
        }),
        this.prisma.tmdbMediaCache.deleteMany({
          where: {
            createdAt: {
              lt: mediaCacheExpiry,
            },
          },
        })
      ]);

      // 清理剩余的过期季缓存（孤立的季缓存）
      await this.prisma.tmdbSeasonCache.deleteMany({
        where: {
          createdAt: {
            lt: mediaCacheExpiry,
          },
        },
      });

      logger.info(
        `缓存清理完成: 搜索缓存 ${deletedSearchCache.count} 条, 媒体缓存 ${deletedMediaCache.count} 条, 季缓存 ${expiredSeasonCount} 条`
      );
    } catch (error) {
      logger.error(`清理过期缓存失败: ${error}`);
      throw new TmdbError(
        '清理过期缓存失败',
        'CACHE_CLEANUP_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 清除所有缓存
   */
  async clearAllCache(): Promise<{ searchCount: number; mediaCount: number; seasonCount: number }> {
    try {
      logger.info('开始清除所有 TMDB 缓存');

      // 先统计季缓存数量（在级联删除之前）
      const totalSeasonCount = await this.prisma.tmdbSeasonCache.count();

      // 并行删除搜索缓存和媒体缓存（媒体缓存删除会级联删除相关季缓存）
      const [searchResult, mediaResult] = await Promise.all([
        this.prisma.tmdbSearchCache.deleteMany({}),
        this.prisma.tmdbMediaCache.deleteMany({})
      ]);

      // 删除剩余的孤立季缓存（如果有的话）
      await this.prisma.tmdbSeasonCache.deleteMany({});

      const result = {
        searchCount: searchResult.count,
        mediaCount: mediaResult.count,
        seasonCount: totalSeasonCount // 使用之前统计的总数
      };

      logger.info(
        `所有缓存清除完成: 搜索缓存 ${result.searchCount} 条, 媒体缓存 ${result.mediaCount} 条, 季缓存 ${result.seasonCount} 条`
      );

      return result;
    } catch (error) {
      logger.error('清除所有缓存失败', error);
      throw new TmdbError(
        '清除所有缓存失败',
        'CACHE_CLEANUP_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 清除所有搜索缓存
   */
  async clearSearchCache(): Promise<number> {
    try {
      logger.info('开始清除所有搜索缓存');

      const result = await this.prisma.tmdbSearchCache.deleteMany({});

      logger.info(`搜索缓存清除完成: ${result.count} 条`);
      return result.count;
    } catch (error) {
      logger.error('清除搜索缓存失败', error);
      throw new TmdbError(
        '清除搜索缓存失败',
        'CACHE_CLEANUP_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 清除所有媒体缓存（会级联删除相关季缓存）
   */
  async clearMediaCache(): Promise<number> {
    try {
      logger.info('开始清除所有媒体缓存');

      const result = await this.prisma.tmdbMediaCache.deleteMany({});

      logger.info(`媒体缓存清除完成: ${result.count} 条 (电视剧的相关季缓存已自动级联删除)`);
      return result.count;
    } catch (error) {
      logger.error('清除媒体缓存失败', error);
      throw new TmdbError(
        '清除媒体缓存失败',
        'CACHE_CLEANUP_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 清除所有季缓存
   */
  async clearSeasonCache(): Promise<number> {
    try {
      logger.info('开始清除所有季缓存');

      const result = await this.prisma.tmdbSeasonCache.deleteMany({});

      logger.info(`季缓存清除完成: ${result.count} 条`);
      return result.count;
    } catch (error) {
      logger.error('清除季缓存失败', error);
      throw new TmdbError(
        '清除季缓存失败',
        'CACHE_CLEANUP_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 清除特定搜索缓存
   */
  async clearSpecificSearchCache(query: string): Promise<boolean> {
    try {
      logger.debug(`清除特定搜索缓存: ${query}`);

      const result = await this.prisma.tmdbSearchCache.deleteMany({
        where: { query }
      });

      const deleted = result.count > 0;
      if (deleted) {
        logger.debug(`搜索缓存已清除: ${query}`);
      } else {
        logger.debug(`搜索缓存不存在: ${query}`);
      }

      return deleted;
    } catch (error) {
      logger.error(`清除特定搜索缓存失败: ${query}`, error);
      throw new TmdbError(
        '清除特定搜索缓存失败',
        'CACHE_CLEANUP_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 清除特定媒体缓存（会级联删除相关季缓存）
   */
  async clearSpecificMediaCache(tmdbId: number): Promise<boolean> {
    try {
      logger.debug(`清除特定媒体缓存: ${tmdbId}`);

      // 使用 delete 而不是 deleteMany 来触发级联删除
      // 首先检查缓存是否存在
      const existingCache = await this.prisma.tmdbMediaCache.findUnique({
        where: { tmdbId }
      });

      if (!existingCache) {
        logger.debug(`媒体缓存不存在: ${tmdbId}`);
        return false;
      }

      // 删除媒体缓存，如果是电视剧，会自动级联删除相关季缓存
      await this.prisma.tmdbMediaCache.delete({
        where: { tmdbId }
      });

      logger.debug(`媒体缓存已清除: ${tmdbId}${existingCache.mediaType === 'TV' ? ' (包括相关季缓存)' : ''}`);
      return true;
    } catch (error) {
      logger.error(`清除特定媒体缓存失败: ${tmdbId}`, error);
      throw new TmdbError(
        '清除特定媒体缓存失败',
        'CACHE_CLEANUP_ERROR',
        undefined,
        error as Error
      );
    }
  }

  /**
   * 清除特定季缓存
   */
  async clearSpecificSeasonCache(tvId: number, seasonNumber?: number): Promise<number> {
    try {
      if (seasonNumber !== undefined) {
        logger.debug(`清除特定季缓存: TV${tvId} S${seasonNumber}`);

        const result = await this.prisma.tmdbSeasonCache.deleteMany({
          where: {
            tvId,
            seasonNumber
          }
        });

        logger.debug(`季缓存清除完成: TV${tvId} S${seasonNumber}, ${result.count} 条`);
        return result.count;
      } else {
        logger.debug(`清除电视剧所有季缓存: TV${tvId}`);

        const result = await this.prisma.tmdbSeasonCache.deleteMany({
          where: { tvId }
        });

        logger.debug(`电视剧季缓存清除完成: TV${tvId}, ${result.count} 条`);
        return result.count;
      }
    } catch (error) {
      logger.error(`清除特定季缓存失败: TV${tvId} S${seasonNumber}`, error);
      throw new TmdbError(
        '清除特定季缓存失败',
        'CACHE_CLEANUP_ERROR',
        undefined,
        error as Error
      );
    }
  }
}
