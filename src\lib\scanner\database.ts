/**
 * 扫描模块的数据库操作服务
 * 负责将扫描结果转换并保存到数据库中的 FileTask 记录
 */

import path from 'path';
import { FileType, FileOperation } from '@prisma/client';
import { getPrismaClient } from '../database';
import { logger } from '../logger';
import { DatabaseError } from '../errors';
import type { VideoFile, SubtitleFile, FontFile, ScanResult } from './types';

/**
 * 扫描结果统计
 */
export interface ScanResultStats {
  /** 创建的视频文件任务数量 */
  videoTasks: number;
  /** 创建的字幕文件任务数量 */
  subtitleTasks: number;
  /** 创建的字体文件任务数量 */
  fontTasks: number;
  /** 总创建的文件任务数量 */
  totalTasks: number;
  /** 忽略的文件数量 */
  ignoredFiles: number;
}

/**
 * 数据库保存进度回调
 */
export interface DatabaseSaveProgress {
  /** 当前保存的批次 */
  currentBatch: number;
  /** 总批次数 */
  totalBatches: number;
  /** 已保存的任务数 */
  savedTasks: number;
  /** 总任务数 */
  totalTasks: number;
  /** 进度百分比 */
  progress: number;
}

/**
 * 将扫描结果保存到数据库
 * @param mediaTaskId 媒体任务ID
 * @param sourcePath 源路径
 * @param scanResult 扫描结果
 * @param fileOperation 文件操作类型
 * @param onProgress 进度回调
 * @returns 保存结果统计
 */
export async function saveScanResultToDatabase(
  mediaTaskId: string,
  sourcePath: string,
  scanResult: ScanResult,
  fileOperation: FileOperation,
  onProgress?: (progress: DatabaseSaveProgress) => void
): Promise<ScanResultStats> {
  const prisma = getPrismaClient();
  const startTime = Date.now();
  
  logger.info(`[数据库保存] 开始保存扫描结果到数据库`, {
    mediaTaskId,
    videoFiles: scanResult.videoFiles.length,
    subtitleFiles: scanResult.subtitleFiles.length,
    fontFiles: scanResult.fontFiles.length,
    ignoredFiles: scanResult.ignoredFiles.length,
  });

  try {
    // 转换扫描结果为 FileTask 记录
    const fileTasksToCreate = [];
    
    // 处理视频文件
    for (const videoFile of scanResult.videoFiles) {
      fileTasksToCreate.push(createFileTaskFromVideo(mediaTaskId, sourcePath, videoFile, fileOperation));
    }

    // 处理独立字幕文件
    for (const subtitleFile of scanResult.subtitleFiles) {
      fileTasksToCreate.push(createFileTaskFromSubtitle(mediaTaskId, sourcePath, subtitleFile, fileOperation));
    }

    // 处理字体文件
    for (const fontFile of scanResult.fontFiles) {
      fileTasksToCreate.push(createFileTaskFromFont(mediaTaskId, sourcePath, fontFile, fileOperation));
    }

    // 批量保存到数据库
    let savedCount = 0;
    const batchSize = 50; // 批量处理大小
    const totalBatches = Math.ceil(fileTasksToCreate.length / batchSize);

    for (let i = 0; i < fileTasksToCreate.length; i += batchSize) {
      const batch = fileTasksToCreate.slice(i, i + batchSize);
      const currentBatch = Math.floor(i / batchSize) + 1;
      
      try {
        await prisma.fileTask.createMany({
          data: batch,
          skipDuplicates: true, // 跳过重复记录
        });
        
        savedCount += batch.length;
        
        if (onProgress) {
          onProgress({
            currentBatch,
            totalBatches,
            savedTasks: savedCount,
            totalTasks: fileTasksToCreate.length,
            progress: Math.round((savedCount / fileTasksToCreate.length) * 100),
          });
        }
        
        logger.debug(`[数据库保存] 批次保存完成: ${savedCount}/${fileTasksToCreate.length}`);
      } catch (error) {
        logger.error(`[数据库保存] 批次保存失败`, { 
          error, 
          batchStart: i, 
          batchSize: batch.length 
        });
        throw new DatabaseError(`保存文件任务失败: ${error instanceof Error ? error.message : '未知错误'}`, {
          mediaTaskId,
          batchStart: i,
          batchSize: batch.length,
          originalError: error,
        });
      }
    }

    // 完成
    const duration = Date.now() - startTime;
    const stats: ScanResultStats = {
      videoTasks: scanResult.videoFiles.length,
      subtitleTasks: scanResult.subtitleFiles.length,
      fontTasks: scanResult.fontFiles.length,
      totalTasks: savedCount,
      ignoredFiles: scanResult.ignoredFiles.length,
    };

    logger.info(`[数据库保存] 保存完成 (${duration}ms)`, {
      mediaTaskId,
      stats,
      duration: `${duration}ms`,
    });

    return stats;

  } catch (error) {
    logger.error(`[数据库保存] 保存失败`, { 
      error, 
      mediaTaskId,
      duration: `${Date.now() - startTime}ms`,
    });

    if (error instanceof DatabaseError) {
      throw error;
    }

    throw new DatabaseError(
      `保存扫描结果失败: ${error instanceof Error ? error.message : '未知错误'}`,
      { mediaTaskId, originalError: error }
    );
  }
}

/**
 * 从视频文件创建 FileTask 记录
 */
function createFileTaskFromVideo(
  mediaTaskId: string,
  sourcePath: string,
  videoFile: VideoFile,
  fileOperation: FileOperation
) {
  // 计算相对路径
  const relativePath = path.relative(sourcePath, videoFile.path);
  
  return {
    mediaTaskId,
    sourcePath: relativePath,
    fileType: videoFile.fileType,
    fileOperation,
    fileSize: videoFile.metadata.fileSize,
    duration: videoFile.metadata.duration || null,
    resolution: videoFile.metadata.resolution || null,
    associatedFiles: videoFile.associatedFiles.length > 0 
      ? videoFile.associatedFiles.map(filePath => path.relative(sourcePath, filePath))
      : null,
  };
}

/**
 * 从字幕文件创建 FileTask 记录
 */
function createFileTaskFromSubtitle(
  mediaTaskId: string,
  sourcePath: string,
  subtitleFile: SubtitleFile,
  fileOperation: FileOperation
) {
  const relativePath = path.relative(sourcePath, subtitleFile.path);
  
  return {
    mediaTaskId,
    sourcePath: relativePath,
    fileType: FileType.SUBTITLE,
    fileOperation,
    fileSize: subtitleFile.fileSize,
    duration: null,
    resolution: null,
    associatedFiles: null,
  };
}

/**
 * 从字体文件创建 FileTask 记录
 */
function createFileTaskFromFont(
  mediaTaskId: string,
  sourcePath: string,
  fontFile: FontFile,
  fileOperation: FileOperation
) {
  const relativePath = path.relative(sourcePath, fontFile.path);
  
  return {
    mediaTaskId,
    sourcePath: relativePath,
    fileType: FileType.FONT,
    fileOperation,
    fileSize: fontFile.fileSize,
    duration: null,
    resolution: null,
    associatedFiles: null,
  };
}

/**
 * 获取媒体任务的文件任务统计
 * @param mediaTaskId 媒体任务ID
 * @returns 文件任务统计信息
 */
export async function getFileTaskStats(mediaTaskId: string): Promise<{
  total: number;
  byStatus: Record<string, number>;
  byType: Record<string, number>;
}> {
  const prisma = getPrismaClient();
  
  try {
    const fileTasks = await prisma.fileTask.findMany({
      where: { mediaTaskId },
      select: {
        status: true,
        fileType: true,
      },
    });

    const byStatus: Record<string, number> = {};
    const byType: Record<string, number> = {};

    for (const task of fileTasks) {
      byStatus[task.status] = (byStatus[task.status] || 0) + 1;
      byType[task.fileType] = (byType[task.fileType] || 0) + 1;
    }

    return {
      total: fileTasks.length,
      byStatus,
      byType,
    };
  } catch (error) {
    logger.error(`[数据库保存] 获取文件任务统计失败`, { error, mediaTaskId });
    throw new DatabaseError(`获取文件任务统计失败: ${error instanceof Error ? error.message : '未知错误'}`, {
      mediaTaskId,
      originalError: error,
    });
  }
}
