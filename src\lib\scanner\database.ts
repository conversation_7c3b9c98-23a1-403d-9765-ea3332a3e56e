/**
 * 扫描模块的数据库集成服务
 * 负责将扫描结果保存到数据库中的 FileTask 记录
 */

import path from 'path';
import { FileType, FileOperation } from '@prisma/client';
import { getPrismaClient } from '../database';
import { logger } from '../logger';
import { ScanError, DatabaseError, ErrorCode } from '../errors';
import { scanMediaFiles } from './index';
import type { ScanOptions, ScanProgress, VideoFile, SubtitleFile, FontFile } from './types';

/**
 * 扫描进度回调类型
 */
export interface DatabaseScanProgress extends ScanProgress {
  /** 当前阶段描述 */
  stage: 'scanning' | 'saving' | 'completed';
  /** 已保存的 FileTask 数量 */
  savedTasks?: number;
  /** 总需要保存的 FileTask 数量 */
  totalTasks?: number;
}

/**
 * 数据库扫描选项
 */
export interface DatabaseScanOptions extends ScanOptions {
  /** 进度回调函数 */
  onProgress?: (progress: DatabaseScanProgress) => void;
}

/**
 * 扫描结果统计
 */
export interface ScanResultStats {
  /** 创建的视频文件任务数量 */
  videoTasks: number;
  /** 创建的字幕文件任务数量 */
  subtitleTasks: number;
  /** 创建的字体文件任务数量 */
  fontTasks: number;
  /** 总创建的文件任务数量 */
  totalTasks: number;
  /** 忽略的文件数量 */
  ignoredFiles: number;
}

/**
 * 将扫描结果保存到数据库
 * @param mediaTaskId 媒体任务ID
 * @param sourcePath 源路径
 * @param fileOperation 文件操作类型
 * @param options 扫描选项
 * @returns 扫描结果统计
 */
export async function scanAndCreateFileTasks(
  mediaTaskId: string,
  sourcePath: string,
  fileOperation: FileOperation,
  options: DatabaseScanOptions = {}
): Promise<ScanResultStats> {
  const prisma = getPrismaClient();
  const startTime = Date.now();
  
  logger.info(`[数据库扫描] 开始扫描并创建文件任务: ${sourcePath}`, {
    mediaTaskId,
    fileOperation,
  });

  try {
    // 阶段1: 执行文件扫描
    if (options.onProgress) {
      options.onProgress({
        stage: 'scanning',
        currentFile: sourcePath,
        processedFiles: 0,
        totalFiles: 0,
        progress: 0,
      });
    }

    const scanResult = await scanMediaFiles(sourcePath, {
      ...options,
      onProgress: (scanProgress) => {
        if (options.onProgress) {
          options.onProgress({
            ...scanProgress,
            stage: 'scanning',
          });
        }
      },
    });

    logger.info(`[数据库扫描] 扫描完成，开始保存到数据库`, {
      videoFiles: scanResult.videoFiles.length,
      subtitleFiles: scanResult.subtitleFiles.length,
      fontFiles: scanResult.fontFiles.length,
      ignoredFiles: scanResult.ignoredFiles.length,
    });

    // 阶段2: 将扫描结果转换为 FileTask 记录
    const fileTasksToCreate = [];
    
    // 处理视频文件
    for (const videoFile of scanResult.videoFiles) {
      fileTasksToCreate.push(createFileTaskFromVideo(mediaTaskId, sourcePath, videoFile, fileOperation));
    }

    // 处理独立字幕文件
    for (const subtitleFile of scanResult.subtitleFiles) {
      fileTasksToCreate.push(createFileTaskFromSubtitle(mediaTaskId, sourcePath, subtitleFile, fileOperation));
    }

    // 处理字体文件
    for (const fontFile of scanResult.fontFiles) {
      fileTasksToCreate.push(createFileTaskFromFont(mediaTaskId, sourcePath, fontFile, fileOperation));
    }

    // 阶段3: 批量保存到数据库
    if (options.onProgress) {
      options.onProgress({
        stage: 'saving',
        currentFile: '保存文件任务到数据库',
        processedFiles: 0,
        totalFiles: fileTasksToCreate.length,
        progress: 0,
        totalTasks: fileTasksToCreate.length,
        savedTasks: 0,
      });
    }

    let savedCount = 0;
    const batchSize = 50; // 批量处理大小

    // 分批保存以避免单次事务过大
    for (let i = 0; i < fileTasksToCreate.length; i += batchSize) {
      const batch = fileTasksToCreate.slice(i, i + batchSize);
      
      try {
        await prisma.fileTask.createMany({
          data: batch,
          skipDuplicates: true, // 跳过重复记录
        });
        
        savedCount += batch.length;
        
        if (options.onProgress) {
          options.onProgress({
            stage: 'saving',
            currentFile: `保存批次 ${Math.floor(i / batchSize) + 1}`,
            processedFiles: savedCount,
            totalFiles: fileTasksToCreate.length,
            progress: Math.round((savedCount / fileTasksToCreate.length) * 100),
            totalTasks: fileTasksToCreate.length,
            savedTasks: savedCount,
          });
        }
        
        logger.debug(`[数据库扫描] 批次保存完成: ${savedCount}/${fileTasksToCreate.length}`);
      } catch (error) {
        logger.error(`[数据库扫描] 批次保存失败`, { 
          error, 
          batchStart: i, 
          batchSize: batch.length 
        });
        throw new DatabaseError(`保存文件任务失败: ${error instanceof Error ? error.message : '未知错误'}`, {
          mediaTaskId,
          batchStart: i,
          batchSize: batch.length,
          originalError: error,
        });
      }
    }

    // 完成
    const duration = Date.now() - startTime;
    const stats: ScanResultStats = {
      videoTasks: scanResult.videoFiles.length,
      subtitleTasks: scanResult.subtitleFiles.length,
      fontTasks: scanResult.fontFiles.length,
      totalTasks: savedCount,
      ignoredFiles: scanResult.ignoredFiles.length,
    };

    if (options.onProgress) {
      options.onProgress({
        stage: 'completed',
        currentFile: '扫描完成',
        processedFiles: savedCount,
        totalFiles: fileTasksToCreate.length,
        progress: 100,
        totalTasks: fileTasksToCreate.length,
        savedTasks: savedCount,
      });
    }

    logger.info(`[数据库扫描] 扫描并保存完成 (${duration}ms)`, {
      mediaTaskId,
      stats,
      duration: `${duration}ms`,
    });

    return stats;

  } catch (error) {
    logger.error(`[数据库扫描] 扫描失败: ${sourcePath}`, { 
      error, 
      mediaTaskId,
      duration: `${Date.now() - startTime}ms`,
    });

    // 根据错误类型抛出相应的错误
    if (error instanceof ScanError || error instanceof DatabaseError) {
      throw error;
    }

    // 处理文件系统相关错误
    if (error instanceof Error) {
      if (error.message.includes('ENOENT') || error.message.includes('not found')) {
        throw new ScanError(
          `扫描路径不存在: ${sourcePath}`,
          ErrorCode.SCAN_PATH_NOT_FOUND,
          { sourcePath, mediaTaskId, originalError: error }
        );
      }
      
      if (error.message.includes('EACCES') || error.message.includes('permission denied')) {
        throw new ScanError(
          `扫描路径访问被拒绝: ${sourcePath}`,
          ErrorCode.SCAN_PATH_ACCESS_DENIED,
          { sourcePath, mediaTaskId, originalError: error }
        );
      }
    }

    throw new ScanError(
      `扫描失败: ${error instanceof Error ? error.message : '未知错误'}`,
      ErrorCode.SCAN_FAILED,
      { sourcePath, mediaTaskId, originalError: error }
    );
  }
}

/**
 * 从视频文件创建 FileTask 记录
 */
function createFileTaskFromVideo(
  mediaTaskId: string,
  sourcePath: string,
  videoFile: VideoFile,
  fileOperation: FileOperation
) {
  // 计算相对路径
  const relativePath = path.relative(sourcePath, videoFile.path);

  return {
    mediaTaskId,
    sourcePath: relativePath,
    fileType: videoFile.fileType,
    fileOperation,
    fileSize: videoFile.metadata.fileSize,
    duration: videoFile.metadata.duration || null,
    resolution: videoFile.metadata.resolution || null,
    associatedFiles: videoFile.associatedFiles.length > 0
      ? videoFile.associatedFiles.map(filePath => path.relative(sourcePath, filePath))
      : null,
  };
}

/**
 * 从字幕文件创建 FileTask 记录
 */
function createFileTaskFromSubtitle(
  mediaTaskId: string,
  sourcePath: string,
  subtitleFile: SubtitleFile,
  fileOperation: FileOperation
) {
  const relativePath = path.relative(sourcePath, subtitleFile.path);

  return {
    mediaTaskId,
    sourcePath: relativePath,
    fileType: FileType.SUBTITLE,
    fileOperation,
    fileSize: subtitleFile.fileSize,
    duration: null,
    resolution: null,
    associatedFiles: null,
  };
}

/**
 * 从字体文件创建 FileTask 记录
 */
function createFileTaskFromFont(
  mediaTaskId: string,
  sourcePath: string,
  fontFile: FontFile,
  fileOperation: FileOperation
) {
  const relativePath = path.relative(sourcePath, fontFile.path);

  return {
    mediaTaskId,
    sourcePath: relativePath,
    fileType: FileType.FONT,
    fileOperation,
    fileSize: fontFile.fileSize,
    duration: null,
    resolution: null,
    associatedFiles: null,
  };
}

/**
 * 获取媒体任务的文件任务统计
 * @param mediaTaskId 媒体任务ID
 * @returns 文件任务统计信息
 */
export async function getFileTaskStats(mediaTaskId: string): Promise<{
  total: number;
  byStatus: Record<string, number>;
  byType: Record<string, number>;
}> {
  const prisma = getPrismaClient();

  try {
    const fileTasks = await prisma.fileTask.findMany({
      where: { mediaTaskId },
      select: {
        status: true,
        fileType: true,
      },
    });

    const byStatus: Record<string, number> = {};
    const byType: Record<string, number> = {};

    for (const task of fileTasks) {
      byStatus[task.status] = (byStatus[task.status] || 0) + 1;
      byType[task.fileType] = (byType[task.fileType] || 0) + 1;
    }

    return {
      total: fileTasks.length,
      byStatus,
      byType,
    };
  } catch (error) {
    logger.error(`[数据库扫描] 获取文件任务统计失败`, { error, mediaTaskId });
    throw new DatabaseError(`获取文件任务统计失败: ${error instanceof Error ? error.message : '未知错误'}`, {
      mediaTaskId,
      originalError: error,
    });
  }
}
