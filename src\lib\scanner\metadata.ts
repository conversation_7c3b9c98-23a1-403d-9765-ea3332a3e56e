/**
 * 视频元数据提取模块
 * 使用 @remotion/media-parser 提取视频文件的本地元数据
 */

import { parseMedia } from '@remotion/media-parser';
import { promises as fs } from 'fs';
import path from 'path';
import { logger } from '../logger';
import type { VideoMetadata } from './types';

/**
 * 提取视频文件元数据
 * @param filePath 视频文件路径
 * @returns 视频元数据
 */
export async function extractVideoMetadata(filePath: string): Promise<VideoMetadata> {
  try {
    logger.debug(`[元数据提取] 开始提取: ${filePath}`);
    
    // 获取文件大小
    const stats = await fs.stat(filePath);
    const fileSize = BigInt(stats.size);
    
    // 使用 @remotion/media-parser 解析视频元数据
    // parseMedia 需要文件路径而不是 Buffer
    const parseResult = await parseMedia({
      src: filePath,
      fields: {
        durationInSeconds: true,
        dimensions: true,
      },
    });
    
    // 提取时长信息
    let duration: number | undefined;
    if (parseResult.durationInSeconds !== null) {
      duration = parseResult.durationInSeconds;
    }
    
    // 提取分辨率信息
    let resolution: string | undefined;
    if (parseResult.dimensions) {
      resolution = `${parseResult.dimensions.width}x${parseResult.dimensions.height}`;
    }
    
    const metadata: VideoMetadata = {
      fileSize,
      duration,
      resolution,
    };
    
    logger.debug(`[元数据提取] 提取成功: ${filePath}`, {
      fileSize: fileSize.toString(),
      duration,
      resolution,
    });
    
    return metadata;
    
  } catch (error) {
    logger.warn(`[元数据提取] 提取失败: ${filePath}`, { error });
    
    // 如果元数据提取失败，至少返回文件大小
    try {
      const stats = await fs.stat(filePath);
      return {
        fileSize: BigInt(stats.size),
      };
    } catch (statError) {
      logger.error(`[元数据提取] 无法获取文件信息: ${filePath}`, { error: statError });
      throw new Error(`无法访问文件: ${filePath}`);
    }
  }
}

/**
 * 批量提取视频文件元数据
 * @param filePaths 视频文件路径列表
 * @param concurrency 并发数量，默认为 3
 * @returns 元数据映射表
 */
export async function extractBatchVideoMetadata(
  filePaths: string[],
  concurrency: number = 3
): Promise<Map<string, VideoMetadata>> {
  const results = new Map<string, VideoMetadata>();
  
  // 分批处理以控制并发
  for (let i = 0; i < filePaths.length; i += concurrency) {
    const batch = filePaths.slice(i, i + concurrency);
    
    const batchPromises = batch.map(async (filePath) => {
      try {
        const metadata = await extractVideoMetadata(filePath);
        return { filePath, metadata };
      } catch (error) {
        logger.error(`[元数据提取] 批量处理失败: ${filePath}`, { error });
        return null;
      }
    });
    
    const batchResults = await Promise.all(batchPromises);
    
    for (const result of batchResults) {
      if (result) {
        results.set(result.filePath, result.metadata);
      }
    }
  }
  
  logger.info(`[元数据提取] 批量提取完成: ${results.size}/${filePaths.length} 个文件成功`);
  
  return results;
}

/**
 * 验证视频文件是否可访问
 * @param filePath 视频文件路径
 * @returns 是否可访问
 */
export async function validateVideoFile(filePath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(filePath);
    
    // 检查是否为文件
    if (!stats.isFile()) {
      logger.warn(`[元数据提取] 路径不是文件: ${filePath}`);
      return false;
    }
    
    // 检查文件大小
    if (stats.size === 0) {
      logger.warn(`[元数据提取] 文件为空: ${filePath}`);
      return false;
    }
    
    return true;
  } catch (error) {
    logger.warn(`[元数据提取] 文件不可访问: ${filePath}`, { error });
    return false;
  }
}

/**
 * 获取文件基本信息（不解析视频内容）
 * @param filePath 文件路径
 * @returns 基本文件信息
 */
export async function getFileBasicInfo(filePath: string): Promise<{
  fileSize: bigint;
  fileName: string;
  extension: string;
}> {
  try {
    const stats = await fs.stat(filePath);
    const fileName = path.basename(filePath);
    const extension = path.extname(filePath);
    
    return {
      fileSize: BigInt(stats.size),
      fileName,
      extension,
    };
  } catch (error) {
    logger.error(`[元数据提取] 无法获取文件基本信息: ${filePath}`, { error });
    throw new Error(`无法访问文件: ${filePath}`);
  }
}

/**
 * 格式化文件大小为人类可读格式
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export function formatFileSize(bytes: bigint): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = Number(bytes);
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 格式化视频时长为人类可读格式
 * @param seconds 秒数
 * @returns 格式化后的时长 (HH:MM:SS)
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}