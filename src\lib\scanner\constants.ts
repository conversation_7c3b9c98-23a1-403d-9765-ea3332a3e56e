/**
 * 文件扫描模块的常量定义
 * 基于参考项目 Bangumi_Auto_Rename 的实现
 */

import { FileType } from '@prisma/client';

/**
 * 支持的视频文件后缀
 */
export const VIDEO_EXTENSIONS = [
  '.mp4',
  '.mkv',
  '.avi',
  '.wmv',
  '.flv',
  '.mov',
  '.mpg',
  '.mpeg',
  '.m4v',
  '.rm',
  '.rmvb',
] as const;

/**
 * 支持的字幕文件后缀
 */
export const SUBTITLE_EXTENSIONS = [
  '.ass',
  '.srt',
  '.vtt',
  '.sub',
  '.idx',
  '.sup',
  '.pgs',
] as const;

/**
 * 支持的字体文件后缀
 */
export const FONT_EXTENSIONS = [
  '.ttf',
  '.otf',
  '.woff',
  '.woff2',
  '.eot',
] as const;

/**
 * 需要忽略的目录名
 */
export const IGNORE_DIRECTORIES = [
  'cd',
  'scan',
  '.git',
  '.svn',
  'node_modules',
  '.DS_Store',
  'Thumbs.db',
] as const;

/**
 * 需要忽略的文件后缀
 */
export const IGNORE_EXTENSIONS = [
  '.rar',
  '.zip',
  '.7z',
  '.tar',
  '.gz',
  '.bz2',
  '.webp',
  '.jpg',
  '.jpeg',
  '.png',
  '.gif',
  '.bmp',
  '.ico',
  '.txt',
  '.nfo',
  '.log',
] as const;

/**
 * 特典视频标识符 (映射到 VIDEO_EXTRA)
 * 包含 OP, ED, NCOP, NCED 等
 */
export const EXTRA_TAGS = [
  'NCOP',
  'NCED',
  'Menu',
  'IV',
  'CM',
  'NC',
  'OP',
  'ED',
  'Advice',
  'Event',
  'Fans',
  '访谈',
  'Preview',
  'Picture Drama',
  '映像',
] as const;

/**
 * 预告片标识符 (映射到 VIDEO_TRAILER)
 */
export const TRAILER_TAGS = [
  'Teaser',
  'PV',
  'Trailer',
  '预告',
] as const;

/**
 * 特别篇标识符 (映射到 VIDEO_SPECIAL)
 * 包含 OVA, OAD, Special, SP 等
 */
export const SPECIAL_TAGS = [
  'OVA',
  'OAD',
  'Special',
  'SP',
  '特典',
  'Chaos no Kakera',
] as const;

/**
 * 用于文件名标签检测的正则表达式模式
 */
export const TAG_PATTERNS = {
  /** 匹配特典视频标签 */
  EXTRA: new RegExp(`\\b(${EXTRA_TAGS.join('|')})\\b`, 'i'),
  /** 匹配预告片标签 */
  TRAILER: new RegExp(`\\b(${TRAILER_TAGS.join('|')})\\b`, 'i'),
  /** 匹配特别篇标签 */
  SPECIAL: new RegExp(`\\b(${SPECIAL_TAGS.join('|')})\\b`, 'i'),
} as const;

/**
 * 文件类型映射表
 */
export const FILE_TYPE_MAPPING = {
  VIDEO_EXTENSIONS,
  SUBTITLE_EXTENSIONS,
  FONT_EXTENSIONS,
} as const;

/**
 * 根据文件扩展名判断文件类型
 */
export function getFileCategory(extension: string): 'video' | 'subtitle' | 'font' | 'other' {
  const ext = extension.toLowerCase();
  
  if (VIDEO_EXTENSIONS.includes(ext as any)) {
    return 'video';
  }
  if (SUBTITLE_EXTENSIONS.includes(ext as any)) {
    return 'subtitle';
  }
  if (FONT_EXTENSIONS.includes(ext as any)) {
    return 'font';
  }
  
  return 'other';
}

/**
 * 检查是否应该忽略的目录
 */
export function shouldIgnoreDirectory(dirName: string): boolean {
  return IGNORE_DIRECTORIES.some(ignoreDir => 
    dirName.toLowerCase().includes(ignoreDir.toLowerCase())
  );
}

/**
 * 检查是否应该忽略的文件
 */
export function shouldIgnoreFile(fileName: string, extension: string): boolean {
  // 检查扩展名
  if (IGNORE_EXTENSIONS.includes(extension.toLowerCase() as any)) {
    return true;
  }
  
  // 检查隐藏文件
  if (fileName.startsWith('.')) {
    return true;
  }
  
  return false;
}