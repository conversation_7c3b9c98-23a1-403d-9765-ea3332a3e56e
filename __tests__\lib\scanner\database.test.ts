/**
 * 扫描模块数据库集成测试
 */

import { PrismaClient, FileType, FileOperation } from '@prisma/client';
import {
  saveScanResultToDatabase,
  getFileTaskStats,
  scanMediaFilesAndSaveToDatabase
} from '../../../src/lib/scanner';
import { ScanError, DatabaseError } from '../../../src/lib/errors';

// 模拟 Prisma 客户端
const mockPrisma = {
  fileTask: {
    createMany: jest.fn(),
    findMany: jest.fn(),
  },
} as unknown as PrismaClient;

// 模拟数据库单例
jest.mock('../../../src/lib/database', () => ({
  getPrismaClient: () => mockPrisma,
}));

// 模拟扫描引擎
jest.mock('../../../src/lib/scanner/index', () => ({
  scanMediaFiles: jest.fn(),
  scanMediaFilesAndSaveToDatabase: jest.requireActual('../../../src/lib/scanner/index').scanMediaFilesAndSaveToDatabase,
  saveScanResultToDatabase: jest.requireActual('../../../src/lib/scanner/database').saveScanResultToDatabase,
  getFileTaskStats: jest.requireActual('../../../src/lib/scanner/database').getFileTaskStats,
}));

import { scanMediaFiles } from '../../../src/lib/scanner/index';

describe('Scanner Database Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('saveScanResultToDatabase', () => {
    const mockMediaTaskId = 'test-media-task-id';
    const mockSourcePath = '/test/source/path';
    const mockFileOperation = FileOperation.COPY;

    it('应该成功保存扫描结果到数据库', async () => {
      // 模拟扫描结果
      const mockScanResult = {
        videoFiles: [
          {
            path: '/test/source/path/video1.mkv',
            fileType: FileType.VIDEO_MAIN,
            metadata: {
              fileSize: BigInt(1000000),
              duration: 1440,
              resolution: '1920x1080',
            },
            associatedFiles: ['/test/source/path/video1.srt'],
          },
        ],
        subtitleFiles: [
          {
            path: '/test/source/path/standalone.srt',
            fileSize: BigInt(50000),
          },
        ],
        fontFiles: [
          {
            path: '/test/source/path/font.ttf',
            fileSize: BigInt(200000),
          },
        ],
        ignoredFiles: ['/test/source/path/ignored.txt'],
      };

      (mockPrisma.fileTask.createMany as jest.Mock).mockResolvedValue({ count: 3 });

      const result = await saveScanResultToDatabase(
        mockMediaTaskId,
        mockSourcePath,
        mockScanResult,
        mockFileOperation
      );

      expect(result).toEqual({
        videoTasks: 1,
        subtitleTasks: 1,
        fontTasks: 1,
        totalTasks: 3,
        ignoredFiles: 1,
      });

      expect(mockPrisma.fileTask.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            mediaTaskId: mockMediaTaskId,
            fileType: FileType.VIDEO_MAIN,
            fileOperation: mockFileOperation,
            fileSize: BigInt(1000000),
            duration: 1440,
            resolution: '1920x1080',
          }),
          expect.objectContaining({
            mediaTaskId: mockMediaTaskId,
            fileType: FileType.SUBTITLE,
            fileOperation: mockFileOperation,
            fileSize: BigInt(50000),
          }),
          expect.objectContaining({
            mediaTaskId: mockMediaTaskId,
            fileType: FileType.FONT,
            fileOperation: mockFileOperation,
            fileSize: BigInt(200000),
          }),
        ]),
        skipDuplicates: true,
      });
    });

    it('应该处理数据库保存失败的情况', async () => {
      const mockScanResult = {
        videoFiles: [],
        subtitleFiles: [],
        fontFiles: [],
        ignoredFiles: [],
      };

      (mockPrisma.fileTask.createMany as jest.Mock).mockRejectedValue(new Error('数据库错误'));

      await expect(
        saveScanResultToDatabase(mockMediaTaskId, mockSourcePath, mockScanResult, mockFileOperation)
      ).rejects.toThrow(DatabaseError);
    });

    it('应该正确处理进度回调', async () => {
      const mockScanResult = {
        videoFiles: [],
        subtitleFiles: [],
        fontFiles: [],
        ignoredFiles: [],
      };

      (mockPrisma.fileTask.createMany as jest.Mock).mockResolvedValue({ count: 0 });

      const progressCallback = jest.fn();

      await saveScanResultToDatabase(
        mockMediaTaskId,
        mockSourcePath,
        mockScanResult,
        mockFileOperation,
        progressCallback
      );

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          progress: 100,
        })
      );
    });
  });

  describe('scanMediaFilesAndSaveToDatabase', () => {
    const mockMediaTaskId = 'test-media-task-id';
    const mockSourcePath = '/test/source/path';
    const mockFileOperation = FileOperation.COPY;

    it('应该成功扫描并保存到数据库', async () => {
      const mockScanResult = {
        videoFiles: [
          {
            path: '/test/source/path/video1.mkv',
            fileType: FileType.VIDEO_MAIN,
            metadata: {
              fileSize: BigInt(1000000),
              duration: 1440,
              resolution: '1920x1080',
            },
            associatedFiles: [],
          },
        ],
        subtitleFiles: [],
        fontFiles: [],
        ignoredFiles: [],
      };

      (scanMediaFiles as jest.Mock).mockResolvedValue(mockScanResult);
      (mockPrisma.fileTask.createMany as jest.Mock).mockResolvedValue({ count: 1 });

      const result = await scanMediaFilesAndSaveToDatabase(
        mockMediaTaskId,
        mockSourcePath,
        mockFileOperation
      );

      expect(result).toEqual({
        videoTasks: 1,
        subtitleTasks: 0,
        fontTasks: 0,
        totalTasks: 1,
        ignoredFiles: 0,
      });
    });

    it('应该处理扫描失败的情况', async () => {
      const mockError = new Error('扫描失败');
      (scanMediaFiles as jest.Mock).mockRejectedValue(mockError);

      await expect(
        scanMediaFilesAndSaveToDatabase(mockMediaTaskId, mockSourcePath, mockFileOperation)
      ).rejects.toThrow(ScanError);
    });
  });

  describe('getFileTaskStats', () => {
    const mockMediaTaskId = 'test-media-task-id';

    it('应该返回正确的文件任务统计', async () => {
      const mockFileTasks = [
        { status: 'PENDING', fileType: 'VIDEO_MAIN' },
        { status: 'PENDING', fileType: 'VIDEO_MAIN' },
        { status: 'COMPLETED', fileType: 'SUBTITLE' },
        { status: 'FAILED', fileType: 'FONT' },
      ];

      (mockPrisma.fileTask.findMany as jest.Mock).mockResolvedValue(mockFileTasks);

      const result = await getFileTaskStats(mockMediaTaskId);

      expect(result).toEqual({
        total: 4,
        byStatus: {
          PENDING: 2,
          COMPLETED: 1,
          FAILED: 1,
        },
        byType: {
          VIDEO_MAIN: 2,
          SUBTITLE: 1,
          FONT: 1,
        },
      });
    });

    it('应该处理数据库查询失败的情况', async () => {
      (mockPrisma.fileTask.findMany as jest.Mock).mockRejectedValue(new Error('数据库错误'));

      await expect(getFileTaskStats(mockMediaTaskId)).rejects.toThrow(DatabaseError);
    });
  });
});
